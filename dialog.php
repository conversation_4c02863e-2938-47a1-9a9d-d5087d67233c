<?php

//////////////////////////////////////////////////////////////////////////////80
// At<PERSON><PERSON> Dialog
//////////////////////////////////////////////////////////////////////////////80
// Copyright (c) Atheos & <PERSON> (Atheos.io), distributed as-is and without
// warranty under the MIT License. See [root]/docs/LICENSE.md for more.
// This information must remain intact.
//////////////////////////////////////////////////////////////////////////////80
// Authors: <AUTHORS>
//////////////////////////////////////////////////////////////////////////////80

require_once("common.php");

//////////////////////////////////////////////////////////////////////////////80
// Verify Session or Key
//////////////////////////////////////////////////////////////////////////////80
Common::checkSession();

$action = POST("action");
$target = POST("target");
$target = Common::cleanPath($target);

if (!$action || !$target) {
    Common::send("error", "Missing target or action.");
}

if (file_exists("components/$target/dialog.php")) {
    require("components/$target/dialog.php");
} elseif (file_exists("plugins/$target/dialog.php")) {
    require("plugins/$target/dialog.php");
} else {
    Common::send("error", "Bad target destination");
}