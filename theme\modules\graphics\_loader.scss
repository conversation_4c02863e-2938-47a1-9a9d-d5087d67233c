.loader {
	width: 100%;
	height: 100%;
	position: relative;

	h2 {
		text-align: center;
		width: 100%;
		position: absolute;
		top: calc(50% - 60px);

		em {
			display: inline-block;
			animation: jump 1500ms infinite ease-in-out;

			@for $i from 0 to 24 {
				&:nth-of-type(#{$i}) {
					animation-delay: 500ms + (200ms * $i);
				}
			}
		}
	}

	.dual-ring {
		display: inline-block;
		position: absolute;
		top: calc(50% - 20px);
		left: calc(50% - 20px);
		width: 40px;
		height: 40px;

		&::before,
		&::after {
			content: " ";
			display: block;
			width: 100%;
			height: 100%;
			margin: -6px;
			border-radius: 50%;
			position: absolute;
		}

		&::before {
			border: 6px solid $blue;
			opacity: 0.2;
			animation: glow 3s linear infinite;
		}

		&::after {
			border: 6px solid $blue;
			border-color: $blue transparent $blue transparent;
			animation: spin 1.5s linear infinite;
		}
	}
}

@keyframes glow {
	0% {
		opacity: 0.2;
	}

	30% {
		opacity: 0.4;
	}

	50% {
		opacity: 0.8;
	}

	70% {
		opacity: 0.4;
	}

	100% {
		opacity: 0.2;
	}
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

@keyframes jump {
	0% {
		transform: translateY(0);
	}

	50% {
		transform: translateY(1px);
	}

	100% {
		transform: translateY(0);
	}
}