$fontColorMajor: var(--fontColorMajor);
$fontColorMinor: var(--fontColorMinor);
$fontColorSmall: var(--fontColorSmall);

$fontSizeH1: 20px;
$fontSizeH2: 20px;
$fontSizeH3: 18px;
$fontSizeH4: 16px;
$fontSizePr: 14px;
$fontSizeSm: 12px;

$foreground: var(--foreground);
$mideground: var(--mideground);
$background: var(--background);
$highlight: var(--shade2);

:root {
	--borderLight: solid 2px var(--shade3);
	--borderHeavy: solid 3px var(--shade5);

	--hoverMajor: var(--colorMinor);
	--hoverMinor: var(--shade5);
	--colorContrast: var(--orange);
}

html.dark {
	--fontColorMajor: var(--shade0);
	--fontColorMinor: var(--shade2);
	--fontColorSmall: var(--shade3);

	--foreground: var(--shade4);
	--mideground: var(--shade6);
	--background: var(--shade8);
}

html.light {
	--fontColorMajor: var(--shade0);
	--fontColorMinor: var(--shade3);
	--fontColorSmall: var(--shade5);

	--foreground: var(--shade7);
	--mideground: var(--shade8);
	--background: var(--shade9);
}

html.red {
	--colorMajor: var(--red);
	--colorMinor: var(--pink);
	--colorSmall: var(--maroon);
}

html.green {
	--colorMajor: var(--green);
	--colorMinor: var(--jade);
	--colorSmall: var(--zucchini);
}

html.blue {
	--colorMajor: var(--blue);
	--colorMinor: var(--indigo);
	--colorSmall: var(--navy);
}

$borderLight: var(--borderLight);
$borderHeavy: var(--borderHeavy);

$colorMajor: var(--colorMajor);
$colorMinor: var(--colorMinor);
$colorSmall: var(--colorSmall);

$hoverMajor: $colorMajor;
$hoverMinor: $highlight;
$hoverPercent: 30%;

$animationDuration: 0.2s;
$transitionDuration: 0.2s;

$scrollbarWidth: 10px;

$topBarHeight: 33px;
$bottomBarHeight: 23px;
$sidebarHandleWidth: 15px;
$filetreeHeight: 280px;

$inputBackground: $shade7;