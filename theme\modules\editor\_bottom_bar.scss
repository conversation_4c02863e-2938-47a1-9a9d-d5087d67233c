#BOTTOM {
	position: relative;
	z-index: 1;
	height: $bottomBarHeight;
	padding: 3px 0;
	background: $foreground;
	color: $fontColorMinor;
	grid-area: bottom;

	&>a,
	&>span {
		// padding: 5px;
		font-size: 12px;
		user-select: none;
		display: inline-block;
		padding: 0 10px;
		line-height: 16px;
		border-left: 1px solid $fontColorMinor;

		&:first-child {
			border-left: none;
		}
	}

	&>a:hover {
		color: #fff;
	}
}

#cursor-position {
	position: absolute;
	right: 30px;
	bottom: 4px;
	font-size: 12px;
	z-index: 99;
	color: #999;
}

#split_menu {
	bottom: $bottomBarHeight;
	left: 5px;
}

#changemode_menu {
	bottom: $bottomBarHeight;
	left: 45px;
	font-size: inherit;
}