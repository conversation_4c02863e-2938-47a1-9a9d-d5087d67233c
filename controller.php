<?php

//////////////////////////////////////////////////////////////////////////////80
// At<PERSON><PERSON> Controller
//////////////////////////////////////////////////////////////////////////////80
// Copyright (c) Athe<PERSON> & <PERSON> (Atheos.io), distributed as-is and without
// warranty under the MIT License. See [root]/docs/LICENSE.md for more.
// This information must remain intact.
//////////////////////////////////////////////////////////////////////////////80
// Authors: <AUTHORS>
//////////////////////////////////////////////////////////////////////////////80

set_error_handler(function($severity, $message, $file, $line) {
	if (error_reporting() & $severity) {
		throw new ErrorException($message, 0, $severity, $file, $line);
	}
});

require_once("common.php");

$action = POST("action");
$target = POST("target");
$target = Common::cleanPath($target);

//////////////////////////////////////////////////////////////////////////////80
// Verify Session or Key
//////////////////////////////////////////////////////////////////////////////80
if ($action !== "authenticate") {
	Common::checkSession();
}

if (!$action || !$target) {
	Common::send(415, "Missing target or action.");
}

if ($target === "i18n" && $action === "init") {
	$cache = array("cache" => $i18n->getCache());
	Common::send(200, $cache);
}

if ($target === "core" && $action === "loadState") {
    $state = Common::loadState();
	Common::send(200, $state);
}


$componentPath = Common::cleanPath("components/$target/controller.php");

if (file_exists("components/$target/controller.php")) {
	require("components/$target/controller.php");
} elseif (file_exists("plugins/$target/controller.php")) {
	require("plugins/$target/controller.php");
} else {
	Common::send(404, "Bad target destination");
}