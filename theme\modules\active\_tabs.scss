#active_file_tabs {
	margin: 0;
	padding: 10px 0 0 18px;
	overflow: hidden;
	list-style-type: none;
	height: $topBarHeight;

	li {
		float: left;
		margin-right: 12px;
		z-index: 2;

		a {
			width: 150px;
			margin-left: -5px;
			text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.6);
			padding-bottom: 1px;
		}
	}

	li::before,
	li::after {
		display: block;
		content: " ";
		position: absolute;
		top: 0;
		height: 100%;
		width: 44px;
		background-color: inherit;
		z-index: -1;
	}

	li::before {
		// right: -36px;
		right: -8px;
		transform: skew(30deg, 0deg);
		box-shadow: rgba(0, 0, 0, 0.1) 3px 2px 5px, inset rgba(255, 255, 255, 0.09) -1px 0;
	}

	li::after {
		left: -10px;
		transform: skew(-30deg, 0deg);
		box-shadow: rgba(0, 0, 0, 0.1) -3px 2px 5px, inset rgba(255, 255, 255, 0.09) 1px 0;
	}

	li.active {
		z-index: 3;
		background-color: $colorMajor;

		a {
			color: $fontColorMajor;

			.subtle {
				color: $fontColorMinor;
			}

			&::before {
				background: linear-gradient(to left, transparent 90%, #{$colorMajor});
			}
		}
	}
}