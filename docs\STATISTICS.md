Atheos Statistics and Total Lines of Code as of September 3, 2023
================================================================================

cloc /var/www/atheos --exclude-dir=.git,.github,ace-editor,data,fonts,langauges,notes,plugins,workspace,vendor

```bash
     257 text files.
     249 unique files.
      13 files ignored.

github.com/AlDanial/cloc v 1.90  T=0.78 s (316.8 files/s, 37116.4 lines/s)
-------------------------------------------------------------------------------
Language                     files          blank        comment           code
-------------------------------------------------------------------------------
JavaScript                      39           1552           1642           7754
PHP                             86           1379           1702           6082
J<PERSON><PERSON>                            23            208              0           4016
Sass                            68            550             55           2861
Markdown                        26            253              0            720
SVG                              1              0              0             31
XML                              1              0              0              9
CSV                              1              0              0              5
CSS                              1              0              0              2
-------------------------------------------------------------------------------
SUM:                           246           3942           3399          21480
-------------------------------------------------------------------------------
```