{"deny": "No", "affirm": "Yes", "enabled": "Enabled", "disabled": "Disabled", "on": "On", "off": "Off", "true": "True", "false": "False", "passed": "PASSED", "failed": "FAILED", "show": "Show", "hide": "<PERSON>de", "login": {"": "<PERSON><PERSON>", "last": "Last Login: %s"}, "logout": "Logout", "rememberMe": "Remember Me", "find": "Find", "find:": "Find:", "replace": {"": "Replace", "all": "Replace All"}, "replace:": "Replace:", "gotoLine": "Goto Line", "moveLine": {"up": "Move Line Up", "down": "Move Line Down"}, "cycleActive": {"next": "Cycle to new file", "prev": "Cycle to previous file"}, "key": {"alt": "Atl", "esc": "Esc", "ctrl": "Ctrl", "shift": "Shift"}, "cut": "Cut", "copy": "Copy", "paste": "Paste", "extract": {"": "Extract", "success": "Successfully extracted archive.", "unable": "Unable to extract archive.", "noOpen": "Unable to open archive.", "noZip": "PHP ZipArchive not installed or enabled.", "noPhar": "PHP PharData not installed or enabled.", "noRar": "PHP Rar Open not installed or enabled.", "noDecomp": "Unable to decompress gzip archive.", "unrecognized": "Unsupported archive type."}, "duplicate": {"": "Duplicate", "name": "Enter new name:", "file": "File Duplicated", "folder": "Folder Duplicated"}, "preview": "Preview", "outsideWorkspace": "File located outside of workspace.", "cancel": "Cancel", "clear": "Clear", "name": "Name", "description": "Description", "author": "Author", "actions": "Actions", "email": "Email", "create": {"": "Create", "type": "Create %s"}, "rename": {"": "<PERSON><PERSON>", "type": "Rename %s"}, "delete": {"": "Delete", "type": "Delete %s", "scope": "Delete files on server?"}, "edit": "Edit", "rescan": "Rescan", "download": "Download", "save": {"": "Save", "all": "Save All", "active": "Save Active File"}, "open": "Open", "close": {"": "Close", "all": "Close All", "active": "Close Active File"}, "closeUnsavedFile": "Close Unsaved File?", "save&close": "Save & Close", "discardChanges": "Discard Changes", "ln": "Ln", "in": "In:", "all": "All", "col": "Col", "help": "Help", "mode": "Mode", "more": "More", "less": "Less", "users": "Users", "search": "Search", "filterTree": "Filter Tree", "prefix": "Prefix", "substring": "Substring", "regex": "Regex", "scout": {"open": "Open Scout", "query": "Query:", "searching": "Searching...", "searchFiles": "Search Files:", "fileTypes": "space seperated file types", "withinWorkSpace": "Within Workspace"}, "noShell": "Shell_exec() command not enabled", "themes": "Themes", "branch": "Branch", "editor": {"": "Editor", "behavior": "Editor Be<PERSON><PERSON>", "style": "Editor Style"}, "extension": "Extension", "extensions": "Extensions", "textmode": "Textmode", "textmodes": "Textmodes", "saveExtensions": "Save Extensions", "extensionSet": "%s is already set", "keybindings": "Keybindings", "plugins": "Plugins", "system": "System", "administration": "Administration", "autoPairComplete": "Auto Pair Completion", "autocomplete": {"": "Autocompletion", "basic": "Basic Autocompletion", "live": "Live Autocompletion"}, "snippets": "Enable Snippets", "wrap": {"": "Line Wrap", "on": "Wrap Lines", "off": "No Wrap"}, "confirm": "Confirm", "explore": "Explore", "continue": "Continue", "analytics": {"": "Analytics", "enabled": "Anonymous Analytics", "send": "Send Analytics", "policy": "While using the Atheos IDE, we ask to collect the above information. We will not share your data with anyone, this data is to understand user needs and improving Atheos IDE. By opting into our analytics program, you agree to the collection and use of information in accordance with this policy. More information can be found at [root]/docs/PRIVACY.md on the Github Repository."}, "uuid": "UUID", "version": "Version", "first_heard": "First Heard", "last_heard": "Last Heard", "totalSessions": "Total Sessions", "totalUsage": "Total Usage", "server_os": "Server", "client_os": "Browser", "timezone": "Timezone", "settings": {"": "Settings", "autosave": "Settings are automatically saved.", "editor": "Editor Settings", "feedback": "Notification Settings", "draft": "Draft Settings", "system": "System Settings", "codegit": "CodeGit Settings"}, "feedback": "Notifications", "toast": {"location": "Toast Location:", "stay": {"success": "Toast success stay time:", "notice": "Toast notice stay time:", "warning": "Toast warning stay time:", "error": "Toast error stay time:"}}, "output": {"location": "Output Location:", "stay": {"success": "Output success stay time:", "notice": "Output notice stay time:", "warning": "Output warning stay time:", "error": "Output error stay time:"}}, "location": {"bl": "Bottom left", "bc": "Bottom center", "br": "Bottom right", "tl": "Top left", "tc": "Top center", "tr": "Top right"}, "manual": "Disable autoclose", "draft": {"": "Draft", "enable": "Enable autosaving files in draft", "interval": "Draft saving interval", "verbose": "Verbose autosaving"}, "account": {"": "Account", "new": "New Account", "create": "Create Account", "delete": "Delete Account", "confirm": "Confirm Account Deletion", "download": "Download Bulk Template", "upload": "Upload Bulk Accounts"}, "account:": "Account:", "username": {"": "Username", "new": "New Username", "create": "Create <PERSON>rna<PERSON>"}, "username:": "Username:", "password": {"": "Password", "new": "New Password", "change": "Change Password", "changeUser": "Change %s's Password", "confirm": "Confirm Password"}, "password:": "Password:", "project": {"": "Project", "current": "Current Project", "noActive": "No Active Projects", "create": "Create Project", "exists": {"path": "Project path already exists.", "name": "Project name already exists."}, "unableCreate": "Cannot create path.", "unableAbsolute": "Unable to create absolute path.", "unablePermissions": "No Read/Write Permission.", "new": "New Project", "list": "Project List", "name": "Project Name", "rename": "Rename Project", "unableRename": "Cannot rename path.", "delete": "Delete Project", "confirm": "Confirm Project Deletion", "loaded": "%s Loaded", "missing": "Project not found", "gitnote": "This will only work if your Git Repo does not require Interactive Authentication and your Server has Git installed."}, "project:": "Project:", "projects": "Projects", "projects:": "Projects:", "editUserACL": "Edit %s's ACL", "workspaceProjects": "Workspace Projects", "accessAllProjects": "Access ALL Projects", "onlySelectedProjects": "Only Selected Projects", "update": {"": "Update", "check": "Update Check", "changes": "Changes on Atheos:", "current": "Your System Is Up To Date."}, "install": "Install", "domain": "Custom Domain", "nightly": "Note: Your installation is a nightly build. Atheos might be unstable.", "yourVersion": "Your Version", "latestVersion": "Latest Version", "downloadAtheos": "Download Atheos", "language": "Language", "collapse": "Collapse", "enableLigatures": "Enable Code Ligatures", "highlightActiveLine": "Highlight Active Line", "fontSize": "Font Size", "pixel": {"": "%spx", "default": "%spx (default)"}, "showInvisibles": "Show Invisibles", "foldWidgets": "Show Fold Widgets", "tabs": {"soft": "Soft Tabs", "size": "<PERSON><PERSON>"}, "keyboardHandler": "Keyboard Handler", "keyboard": {"default": "<PERSON><PERSON><PERSON>", "vim": "Vim", "emacs": "Emacs", "sublime": "Sublime"}, "userList": "User List", "folder": {"": "Folder", "invalid": "Invalid Folder"}, "directory": {"": "Directory", "invalid": "Invalid Directory"}, "file": {"": "File", "new": "New File", "type": "File Type:", "saved": "File saved"}, "fileNew": "New File", "fileType": "File Type:", "fileSaved": "File saved", "fileUpload": "Upload File", "filesUpload": "Upload Files", "folderNew": "New Folder", "path": {"": "Path", "missing": "Path doesn't exist.", "invalid": "Path isn't valid..", "exists": "Path already exists.", "unableCreate": "Cannot create path.", "unableRename": "Cannot rename path."}, "restricted": {"": "Restricted", "userList": "Not authorized to edit UserList", "updates": "You can not check for updates", "marketplace": "You can not access the Marketplace", "textmodes": "You can not access the Textmodes"}, "unauthorized": "You are not Authorized to %s", "wrapLines": "Wrap Lines", "overscroll": "Overscroll", "printMarginShow": "Print Margin", "printMarginColumn": "Print Margin Column", "theme": "Theme", "hover": {"": "Hover", "duration": "Sidebar Hover Duration"}, "click": {"": "Click", "single": "Single Click", "double": "Double Click"}, "loop": {"behavior": "Active File Loop Behavior", "onlyActive": "Loop only active tabs", "incDropdown": "Include dropdown menu"}, "showHidden": "Show Hidden Files", "trigger": {"filetree": "Filetree Trigger", "projectDock": "Project<PERSON>ock Trigger", "rightSidebar": "Right Sidebar Trigger", "leftSidebar": "Left Sidebar Trigger"}, "contextMenu": {"delay": "Context <PERSON>u <PERSON> Delay"}, "newExtension": "New Extension", "fileExtension": "File Extension", "defaultTextmode": "Default Textmode", "indentGuides": "Indent Guides", "inlinePreview": "Inline Preview", "close_uploader": "Close Uploader", "close_modal": "Close Modal", "installationError": "Installation Error", "existsAndWriteable": "Please make sure the following exist and are writeable:", "envVariablesSet": "Please make sure these environmental variables are set:", "retest": "Re-Test", "initialSetup": "Initial Setup", "developmentMode": "Development Mode", "connectionError": "Connection Error", "market": {"": "Marketplace", "atheos": "Atheos Marketplace", "install": {"gitRepo": "... from Git Repo", "manually": "Install Manually", "success": "Successfully installed %s"}, "unableDownload": "Unable to download Archive.", "noZip": "Zip Extension not found.", "missingDesc": "Missing Description.", "missingAuth": "Unknown"}, "complete": "Complete!", "installed": "Installed", "available": "Available", "coming": "Coming Soon", "reloadAtheos": "Reload Atheos", "split": {"": "Split", "h": "Split Horizontally", "v": "Split Vertically", "editor": {"h": "Split Editor Horizontally", "v": "Split Editor Vertically"}}, "merge": {"": "<PERSON><PERSON>", "all": "Merge all", "editor": "Merge All Editor Panes"}, "time": {"ms": {"": "%sms", "default": "%sms (default)"}, "second": {"single": "%s Second", "plural": "%s Seconds"}, "minute": {"single": "%s Minute", "plural": "%s Minutes"}, "hour": {"single": "%s Hour", "plural": "%s Hours"}}, "warning": {"fileOpen": "Warning: File Currently Opened By: %s"}, "NothingInYourClipboard": "Nothing in Your Clipboard", "folderNameOrAbsolutePath": "Folder Name Or Absolute Path", "noOpenFilesOrSelect": "No Open Files or Selected Text", "dragFilesOrClickHereToUpload": "Drag Files Or Click Here To Upload", "filetree": "File Tree", "gitRepository": "Git Repository", "macro": {"": "Macro Editor", "add": "Add New Macro", "title": "Title", "icon": "FA Icon", "applies": "Applies To", "exts": "File Types", "cmd": "Shell Command"}, "codegit": {"": "CodeGit", "open": "Open CodeGit", "banner": "Banner above File Tree", "status": "File Status in BottomBar", "unknown": "Unknown", "untracked": "Untracked", "uncommitted": "Uncommitted", "committed": "Committed", "error": {"statusFail": "Unable to load Repository status"}, "diff": "<PERSON><PERSON>er", "blame": "<PERSON><PERSON><PERSON> Viewer", "log": "Log Viewer"}, "git": {"error": {"emailRequired": "You have to provide username and email", "noRepo": "Git repo not found.", "noPath": "Missing path.", "noRepoFileMsg": "Missing repo, file or message.", "noRepoFile": "Missing repo or file.", "noPathUrl": "Missing path or URL.", "invalidTransferType": "Invalid git transfer type.", "noTypeRepoRemoteBranch": "Missing type, repo, remote or branch.", "noRepoType": "Missing repo or type.", "invalidAction": "Invalid action."}, "init": {"": "Git Init", "success": "Initialized empty Git repository.", "failed": "Failed to initialize repository."}, "clone": {"": "<PERSON><PERSON>", "success": "Successfully cloned repository.", "failed": "Failed to clone repository."}, "repoURL": {"": "Github Repository URL:", "invalid": "Invalid Repository URL"}, "status": {"": "Commit Status:", "current": "Branch is up to date", "ahead": {"single": "Ahead %s Commit", "plural": "Ahead %s Commits"}, "behind": {"single": "Behind %s Commit", "plural": "Behind %s Commits"}}, "diff": "<PERSON><PERSON>", "blame": "<PERSON><PERSON>", "log": "Git Log", "undo": {"": "<PERSON><PERSON>", "file": "Undo changes on %s?", "success": "Changes reverted.", "failed": "Unable to revert changes."}, "pull": {"": "<PERSON><PERSON>", "success": "Repo pull successful.", "failed": "Repo pull failed."}, "push": {"": "<PERSON><PERSON>", "success": "Repo push successful.", "failed": "Repo push failed."}, "fetch": {"": "<PERSON>tch", "success": "Repo fetch successful.", "failed": "Repo fetch failed."}, "checkout": {"": "Checkout", "success": "Checkout successful.", "failed": "Checkout failed."}, "remote": {"new": "New Remote"}, "branch": {"new": "New Branch"}, "transfer": {"": "Git Transfer", "edit": "Edit Transfer Options"}, "addFailed": "Failed to add %s", "commit": {"": "Commit", "message": "Enter commit message here (optional to amend)...", "success": "Changes commited.", "failed": "Changes could not be committed."}, "amend": {"": "Amend", "success": "Changes commited with amend option.", "failed": "Changes could not be committed with amend option."}, "settings": {"local": "Git Settings for %s", "global": "Global Git Settings", "save": {"success": "Setting<PERSON> saved.", "failed": "Unabled to save settings."}, "apply": {"success": "Settings Applied.", "failed": "Unabled to apply settings."}}, "objects": {"status": "Status", "file": "File", "actions": {"": "Actions", "diff": "Diff", "undo": "Undo"}}}, "overview": "Overview", "log": "Log", "transfer": "Transfer", "configure": "Configure"}