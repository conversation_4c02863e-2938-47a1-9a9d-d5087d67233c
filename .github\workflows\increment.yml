# Run on push to main
on:
  push:
    branches:
      - main
    paths-ignore:
      - '.version'
# Naming
name: Increment Version Number
jobs:
  increment:
    runs-on: ubuntu-latest
    steps:
      - name: Check out source code
        uses: actions/checkout@v3
        with:
          ref: ${{ github.ref }}
          token: ${{ secrets.GITHUB_TOKEN }}
      - name: Increment version automatically
        uses: yoichiro/gh-action-increment-value@main
        with:
          target_directory: ''
          target_file: '.version'
          prefix: 'version":'
          suffix: '}'
          commit_message: 'Increment the build number to '
