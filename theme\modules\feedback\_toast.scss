toaster {
	min-width: 200px;
	max-width: 480px;
	z-index: 99998;
	position: fixed;
	text-align: left !important;

	toast {
		background: var(--mideground);
		padding: 5px;
		font-size: 0.9rem;
		border: $borderLight;
		display: block;
		margin: 5px;
		transition: opacity ease-in-out 300ms;
		opacity: 0;
		cursor: pointer;

		&.active {
			opacity: 1;
		}

		.message {
			text-align: left;
			margin: 0 5px;
			display: inline-block;
		}

		i.success {
			color: $green;
		}

		i.notice {
			color: $cyan;
		}

		i.warning {
			color: $orange;
		}

		i.error {
			color: $red;
		}
	}
}