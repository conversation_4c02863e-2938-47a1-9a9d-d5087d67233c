.sidebar {
	position: relative;
	top: 0;
	height: 100%;
	z-index: 99990;
	-moz-user-select: none;
	-khtml-user-select: none;
	-webkit-user-select: none;
	user-select: none;
	background-color: $background;
	transition: right $transitionDuration ease-in-out, left $transitionDuration ease-in-out;
	white-space: nowrap;

	&.unlocked {
		grid-area: none;
		position: fixed;
	}
}

#SBLEFT {
	width: 350px;
	left: 0;
	grid-area: leftsb;
}

#SBRIGHT {
	width: 250px;
	right: 0;
	grid-area: rightsb;

	.category {
		font-size: $fontSizeH3;
		font-weight: 600;
		color: $colorMajor;
		padding-bottom: 5px;
		margin-left: 5px;
	}

	a {
		margin-left: 5px;
		display: block;
		min-width: 100%;
		padding: 5px;

		&:hover {
			background: var(--hoverMinor);

		}
	}

	hr {
		height: 0;
		border: none;
		border-top: $borderLight;
		padding: 0;
		margin: 10px 0;
	}

}

@media (max-width: 576px) {
    #workspace #SBLEFT {
        width:80vw;
    }
    #workspace #SBRIGHT {
        width:80vw;
    }
}

@import "content", "handle", "project_dock", "title";