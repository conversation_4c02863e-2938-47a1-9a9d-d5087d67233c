<?php

//////////////////////////////////////////////////////////////////////////////80
// Scout Controller
//////////////////////////////////////////////////////////////////////////////80
// Copyright (c) Atheos & <PERSON> (Atheos.io), distributed as-is and without
// warranty under the MIT License. See [root]/docs/LICENSE.md for more.
// This information must remain intact.
//////////////////////////////////////////////////////////////////////////////80
// Authors: <AUTHORS>
//////////////////////////////////////////////////////////////////////////////80

require_once('class.scout.php');

$path = POST("path");

//////////////////////////////////////////////////////////////////////////////80
// Security Check
//////////////////////////////////////////////////////////////////////////////80
$path = Common::getWorkspacePath($path);

//////////////////////////////////////////////////////////////////////////////80
// Create Scout to handle Action
//////////////////////////////////////////////////////////////////////////////80
$Scout = new Scout();

switch ($action) {
	//////////////////////////////////////////////////////////////////////////80
	// Filter File Tree
	//////////////////////////////////////////////////////////////////////////80
	case "filter":
		$strategy = POST("strategy");
		$keyword = POST("keyword");

		$Scout->filter($path, $keyword, $strategy);
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Probe File Contents
	//////////////////////////////////////////////////////////////////////////80
	case "probe":
		$Scout->probe();
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Default: Invalid Action
	//////////////////////////////////////////////////////////////////////////80
	default:
		Common::send(416, "Invalid action.");
		break;
}