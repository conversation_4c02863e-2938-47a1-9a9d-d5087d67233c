@import "modules/util/reset";
@import "modules/util/mixins";
@import "modules/util/colors";
@import "modules/util/shades";
@import "modules/util/variables";
@import "modules/util/animations";

@import "modules/util/scrollbars";

@import "modules/common/checkbox", "modules/common/optionsmenu", "modules/common/input", "modules/common/button";
@import "modules/common/dropdown";
@import "modules/common/fieldset";
@import "modules/common/hint";
@import "modules/common/label";
@import "modules/common/menu";
@import "modules/common/panel";

@import "modules/common/table";
@import "modules/common/toggle";
@import "modules/common/toolbar";
@import "modules/common/typography";

@import "modules/ace/prompt";
@import "modules/ace/search";
@import "modules/ace/settings";

@import "modules/active/main";
@import "modules/codegit/main";
@import "modules/editor/main";
@import "modules/feedback/main";
@import "modules/filetree/main";
@import "modules/sidebar/main";

// @import "modules/graphics/icons";

@import "modules/textmode";
@import "modules/macro";
@import "modules/market";
@import "modules/graphics/icons", "modules/graphics/loader", "modules/graphics/logo";
@import "modules/misc/projects";
@import "modules/misc/contextmenu", "modules/misc/scout", "modules/misc/uploader";
@import "modules/misc/login", "modules/misc/myth", "modules/misc/installer";
@import "modules/misc/update", "modules/misc/analytics", "modules/misc/keybindings";
@import "modules/settings", "modules/user";

// @import "modules/util/cursor";

/* DOCUMENT */
body,
html {
	width: 100%;
	height: 100%;
	color: $shade0;
	background-color: $background;
	font: normal $fontSizePr 'Ubuntu', sans-serif;
	// overflow: hidden;
}

/* MAIN */
#workspace {
	background-color: $background;
	margin: 0;
	padding: 0;
	width: 100%;
	height: 100%;
	z-index: 1;
	overflow: hidden;

	display: grid;
	grid-template-rows: auto 1fr auto;
	grid-template-columns: auto 1fr auto;

	grid-template-areas:
		"leftsb active rightsb"
		"leftsb editor rightsb"
		"leftsb bottom rightsb";
}

/* Download iFrame */
#download {
	display: none;
}