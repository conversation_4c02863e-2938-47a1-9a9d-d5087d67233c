<?php

//////////////////////////////////////////////////////////////////////////////80
// Draft Controller
//////////////////////////////////////////////////////////////////////////////80
// Copyright (c) Athe<PERSON> & <PERSON> (Atheos.io), distributed as-is and without
// warranty under the MIT License. See [root]/docs/LICENSE.md for more.
// This information must remain intact.
//////////////////////////////////////////////////////////////////////////////80
// Authors: <AUTHORS>
//////////////////////////////////////////////////////////////////////////////80

require_once("class.draft.php");

$activeUser = SESSION("user");
$path = POST("path");

//////////////////////////////////////////////////////////////////////////////80
// Security Check
//////////////////////////////////////////////////////////////////////////////80
$path = Common::getWorkspacePath($path);

//////////////////////////////////////////////////////////////////////////////80
// Handle Action
//////////////////////////////////////////////////////////////////////////////80
$Draft = new Draft($activeUser);

switch ($action) {
	//////////////////////////////////////////////////////////////////////////80
	// Save
	//////////////////////////////////////////////////////////////////////////80
	case "check":
		$Draft->check($path);
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Delete
	//////////////////////////////////////////////////////////////////////////80
	case "delete":
		$Draft->delete($path);
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Open
	//////////////////////////////////////////////////////////////////////////80
	case "open":
		$Draft->open($path);
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Save
	//////////////////////////////////////////////////////////////////////////80
	case "save":
		$content = POST("content");
		$Draft->save($path, $content);
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Default: Invalid Action
	//////////////////////////////////////////////////////////////////////////80
	default:
		Common::send(416, "Invalid action.");
		break;
}