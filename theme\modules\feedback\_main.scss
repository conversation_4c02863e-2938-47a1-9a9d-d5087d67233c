overlay {
	display: none;
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.6);
	z-index: 99990;
	opacity: 0;

	&.active {
		display: block;
		opacity: 1;
	}
}

#alert,
#dialog {

	.drag,
	.close {
		position: absolute;
		font-size: $fontSizeH2;
		padding: 0;
		color: $fontColorMinor;
		top: 5px;
	}

	.drag {
		right: 28px;
		cursor: grab;

		&.active {
			cursor: grabbing;
		}
	}

	.close {
		right: 5px;
		padding: 0;
		cursor: pointer;
	}
}

toaster,
output {
	&.top {
		top: 20px;
	}

	&.bottom {
		bottom: 30px;
	}

	&.left {
		left: 20px;
	}

	&.center {
		left: 50%;
		margin-left: -140px;
	}

	&.right {
		right: 20px;
	}
}

@import "alert", "dialog", "output", "toast";