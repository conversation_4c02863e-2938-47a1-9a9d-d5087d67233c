<?php

//////////////////////////////////////////////////////////////////////////////80
// Settings Controller
//////////////////////////////////////////////////////////////////////////////80
// Copyright (c) 2020 <PERSON> (<EMAIL>), distributed as-is and without
// warranty under the MIT License. See [root]/docs/LICENSE.md for more.
// This information must remain intact.
//////////////////////////////////////////////////////////////////////////////80
// Authors: <AUTHORS>
//////////////////////////////////////////////////////////////////////////////80

require_once('class.settings.php');

$activeUser = SESSION("user");

$Settings = new Settings($activeUser);

switch ($action) {

	//////////////////////////////////////////////////////////////////////////80
	// Load User Settings
	//////////////////////////////////////////////////////////////////////////80
	case "load":
		$Settings->load();
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Save User Settings
	//////////////////////////////////////////////////////////////////////////80
	case "save":
		$key = POST("key");
		$value = POST("value");
		if ($key && $value) {
			$Settings->save($key, $value);
		} else {
			Common::send(417, "Missing key or value.");
		}
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Default: Invalid Action
	//////////////////////////////////////////////////////////////////////////80
	default:
		Common::send(416, "Invalid action.");
		break;
}