<?php

//////////////////////////////////////////////////////////////////////////////80
// File Tree Controller
//////////////////////////////////////////////////////////////////////////////80
// Copyright (c) At<PERSON><PERSON> & <PERSON> (Atheos.io), distributed as-is and without
// warranty under the MIT License. See [root]/docs/LICENSE.md for more.
// This information must remain intact.
//////////////////////////////////////////////////////////////////////////////80
// Authors: <AUTHORS>
//////////////////////////////////////////////////////////////////////////////80

require_once("class.filetree.php");

$dest = POST("dest");
$name = POST("name");
$path = POST("path");

//////////////////////////////////////////////////////////////////////////////80
// Security Check
//////////////////////////////////////////////////////////////////////////////80
$path = Common::getWorkspacePath($path);
$dest = Common::getWorkspacePath($dest);

//////////////////////////////////////////////////////////////////////////////80
// Handle Action
//////////////////////////////////////////////////////////////////////////////80
$FileTree = new FileTree();

switch ($action) {
	//////////////////////////////////////////////////////////////////////////80
	// Create
	//////////////////////////////////////////////////////////////////////////80
	case "create":
	    $type = POST("type");
		$FileTree->create($path, $type);
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Delete
	//////////////////////////////////////////////////////////////////////////80
	case "delete":
		$FileTree->delete($path);
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Duplicate
	//////////////////////////////////////////////////////////////////////////80
	case "duplicate":
		$FileTree->duplicate($path, $dest);
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Extract
	//////////////////////////////////////////////////////////////////////////80
	case "extract":
		$name = POST("fileName");
		$FileTree->extract($path, $name);
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Index
	//////////////////////////////////////////////////////////////////////////80
	case "index":
		$FileTree->index($path);
		break;

	//////////////////////////////////////////////////////////////////////////80
	// loadURL for file preview
	//////////////////////////////////////////////////////////////////////////80
	case "loadURL":
		$FileTree->loadURL($path);
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Move
	//////////////////////////////////////////////////////////////////////////80
	case "move":
		$FileTree->move($path, $dest);
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Rename
	//////////////////////////////////////////////////////////////////////////80
	case "rename":
		$FileTree->rename($path, $name);
		break;

	//////////////////////////////////////////////////////////////////////////80
	// Default: Invalid Action
	//////////////////////////////////////////////////////////////////////////80
	default:
		Common::send(416, "Invalid action.");
		break;
}