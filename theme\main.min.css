html,body,div,span,applet,object,iframe,h1,h2,h3,h4,h5,h6,p,blockquote,pre,abbr,acronym,address,big,cite,code,del,dfn,em,img,ins,kbd,q,s,samp,small,strike,strong,sub,sup,tt,var,b,u,i,center,dl,dt,dd,ol,ul,li,fieldset,form,label,legend,table,caption,tbody,tfoot,thead,tr,th,td,article,aside,canvas,details,figcaption,figure,footer,header,hgroup,menu,nav,section,summary,time,mark,audio,video{margin:0;padding:0;border:0;outline:0;font-size:100%;font:inherit;vertical-align:baseline}a{margin:0;padding:0;font-size:100%;vertical-align:baseline;background:transparent}article,aside,details,figcaption,figure,footer,header,hgroup,menu,nav,section{display:block}body{line-height:1}ol,ul{list-style:none}blockquote,q{quotes:none}blockquote:before,blockquote:after,q:before,q:after{content:'';content:none}ins{text-decoration:none}del{text-decoration:line-through}table{border-collapse:collapse;border-spacing:0}*{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box;line-height:100%}.hidden{height:100%;position:absolute;pointer-events:none;border:none;padding:0;margin:0;right:0;top:0;opacity:0}:root{--pink:#FF4347;--red:#FF483A;--maroon:#4F2710;--orange:#F08D24;--yellow:#FFD60A;--lime:#01FF70;--jade:#2BAF4A;--green:#4EBA0A;--zucchini:#003f1f;--teal:#008080;--aqua:#7FDBFF;--cyan:#1CC3E8;--indigo:#3E5CC6;--blue:#0074D9;--navy:#002F6F;--fushia:#F012BE;--purple:#BF5AF2}html.light{--red:#CC3A2F;--orange:#C0701C;--yellow:#CCAC08;--lime:#00CC5C;--green:#3A8C07;--aqua:#5FC0E0;--cyan:#149BC2;--indigo:#2E47A3;--blue:#005FB0;--navy:#002F6F;--fushia:#C00F98;--purple:#9845C2}.red::before{color:var(--red)}.orange::before{color:var(--orange)}.yellow::before{color:var(--yellow)}.lime::before{color:var(--lime)}.green::before{color:var(--green)}.teal::before{color:var(--teal)}.aqua::before{color:var(--aqua)}.cyan::before{color:var(--cyan)}.indigo::before{color:var(--indigo)}.blue::before{color:var(--blue)}.navy::before{color:var(--navy)}.maroon::before{color:var(--maroon)}.fushia::before{color:var(--fushia)}.purple::before{color:var(--purple)}.pink::before{color:var(--pink)}span.red{color:#FF483A}span.orange{color:#F08D24}span.yellow{color:#FFD60A}span.lime{color:#01FF70}span.green{color:#4EBA0A}span.teal{color:#008080}span.aqua{color:#7FDBFF}span.cyan{color:#1CC3E8}span.indigo{color:#3E5CC6}span.blue{color:#0074D9}span.navy{color:#001f3f}span.maroon{color:#85443B}span.fushia{color:#F012BE}span.purple{color:#BF5AF2}span.pink{color:#FF375F}:root{--shade0:#F5F9F9;--shade1:#D5D9DD;--shade2:#A5A9AA;--shade3:#757577;--shade4:#414141;--shade5:#3A3A3A;--shade6:#252525;--shade7:#1C1C1E;--shade8:#151516;--shade9:#010101}html.light{--shade0:#010101;--shade1:#151516;--shade2:#252525;--shade3:#3A3A3A;--shade4:#414141;--shade5:#858587;--shade6:#A5A9AA;--shade7:#D5D5DD;--shade8:#E4E4E9;--shade9:#F5F5F9}.shade0::before{color:var(--shade0)}.shade1::before{color:var(--shade1)}.shade2::before{color:var(--shade2)}.shade3::before{color:var(--shade3)}.shade4::before{color:var(--shade4)}.shade5::before{color:var(--shade5)}.shade6::before{color:var(--shade6)}.shade7::before{color:var(--shade7)}.shade8::before{color:var(--shade8)}.shade9::before{color:var(--shade9)}:root{--borderLight:solid 2px var(--shade3);--borderHeavy:solid 3px var(--shade5);--hoverMajor:var(--colorMinor);--hoverMinor:var(--shade5);--colorContrast:var(--orange)}html.dark{--fontColorMajor:var(--shade0);--fontColorMinor:var(--shade2);--fontColorSmall:var(--shade3);--foreground:var(--shade4);--mideground:var(--shade6);--background:var(--shade8)}html.light{--fontColorMajor:var(--shade0);--fontColorMinor:var(--shade3);--fontColorSmall:var(--shade5);--foreground:var(--shade7);--mideground:var(--shade8);--background:var(--shade9)}html.red{--colorMajor:var(--red);--colorMinor:var(--pink);--colorSmall:var(--maroon)}html.green{--colorMajor:var(--green);--colorMinor:var(--jade);--colorSmall:var(--zucchini)}html.blue{--colorMajor:var(--blue);--colorMinor:var(--indigo);--colorSmall:var(--navy)}@keyframes spin{from{transform:rotate(0deg)}to{transform:rotate(360deg)}}@keyframes shake{10%,90%{transform:translate(-1px, 0) rotate(-5deg)}20%,80%{transform:translate(1px, 0) rotate(5deg)}30%,50%,70%{transform:translate(-2px, 0) rotate(-5deg)}40%,60%{transform:translate(2px, 0) rotate(5deg)}}::-webkit-scrollbar{width:10px;height:10px}table{scrollbar-width:none}table ::-webkit-scrollbar{display:none}::-webkit-scrollbar-track-piece{background-color:transparent}::-webkit-scrollbar-thumb:vertical{height:5px;background-color:var(--shade4)}::-webkit-scrollbar-thumb:vertical:hover{background-color:var(--shade3)}::-webkit-scrollbar-thumb:horizontal{width:5px;background-color:var(--shade4)}::-webkit-scrollbar-thumb:horizontal:hover{background-color:var(--shade3)}::-webkit-scrollbar-corner{background-color:transparent}*{scrollbar-width:thin;scrollbar-color:var(--shade4) transparent}input[type="checkbox"]{width:1rem;height:1rem;position:relative;font-size:1rem;line-height:1rem;font-family:'Font Awesome 6 Free';cursor:pointer;color:var(--shade7)}input[type="checkbox"].large{width:1.5rem;height:1.5rem;font-size:1.5rem;line-height:1.5rem}input[type="checkbox"]::after{content:"";position:absolute;top:0;right:0;bottom:0;left:0;color:rgba(var(--background));background-color:var(--fontColorMajor);box-shadow:inset 0 0 0 1px var(--background);font-size:0;font-weight:bolder;text-align:center;transition:background-color 150ms 0ms ease, color 150ms 50ms ease, font-size 150ms 50ms ease}input[type="checkbox"]:checked::after{content:"\f00d";color:rgba(var(--background));font-size:1em}.options-menu{position:absolute;z-index:9999;background-color:var(--background);border:var(--borderLight)}.options-menu td{padding:0;margin:0;vertical-align:unset;border:none}.options-menu li{box-shadow:inset 0 0 0 1px var(--foreground);padding:0;color:var(--fontColorMinor)}.options-menu li a{display:block;padding:10px 5px}.options-menu li.active{font-weight:700;background:var(--shade2);color:white}.options-menu li:hover{background-color:var(--shade2);color:white}#dependencies,button,dropdown,input,select,textarea{width:100%;margin:5px 0;padding:7px 10px;line-height:normal;border-radius:0;background-color:var(--shade7);border:none;border-bottom:var(--borderLight);color:var(--fontColorMajor)}select option{background-color:var(--shade7)}textarea{min-height:100px}input:disabled,select:disabled,textarea:disabled{background-color:var(--foreground);color:var(--fontColorMinor)}input:focus,select:focus,textarea:focus{outline:none;border-bottom:2px solid var(--shade2)}input.cat1,input.cat2{background-color:var(--red)}input.cat3{background-color:var(--orange)}input.cat4{background-color:var(--yellow)}input.cat5{background-color:var(--green)}button{display:inline-block;width:auto;margin:3px 0;min-width:80px;color:var(--background);font-weight:bold;background-color:var(--fontColorMajor);cursor:pointer}button.bottom-left{position:absolute;bottom:10px;left:10px}button:disabled{color:var(--shade2);cursor:unset}button:disabled:hover{color:var(--shade2);background-color:var(--fontColorMajor);border-color:var(--shade3)}button:hover{background-color:var(--fontColorMinor);border-color:var(--shade4)}button:focus{outline:none}button+button{margin-left:6px}dropdown{width:100%;position:absolute;top:0;left:0;max-height:2em;cursor:pointer;text-align:left;overflow:hidden;outline:none;transition:0.3s all ease-in-out;z-index:99}dropdown input:focus+label{background:#def}dropdown input{width:1px;height:1px;display:inline-block;position:absolute;opacity:0.01}dropdown label{height:2em;line-height:2em;padding-left:1em;padding-right:3em;cursor:pointer;position:relative;transition:0.3s color ease-in-out}dropdown label:nth-child(2){margin-top:2em;border-top:0.06em solid #d9d9d9}dropdown input:checked+label{display:block;position:absolute;top:0;width:100%}dropdown input:checked+label:nth-child(2){margin-top:0;position:relative}dropdown.expanded{max-height:15em}dropdown.expanded label:hover{color:#3694d7}dropdown.expanded input:checked+label{color:#3694d7}legend{text-align:center;font-size:1.2em;font-weight:700}hint{font-size:12px;color:var(--fontColorMinor);position:absolute;bottom:5px;left:5px}label{display:block}label.title{font-size:20px;padding:2px 8px;font-weight:700;background-color:var(--foreground)}label.dialog{font-size:18px}menu{max-height:55vh;overflow:auto;width:200px}menu li{list-style:none}menu li a{display:block;padding:10px;user-select:none}menu .active{background-color:var(--colorMajor)}panel{padding:5px;width:calc(100% - 200px);overflow:auto;overflow-y:scroll;height:500px;position:relative;border-left:var(--borderLight)}panel form{padding:0}panel>label:first-of-type{font-size:20px;padding:3px 0;margin-bottom:3px;border-bottom:var(--borderHeavy)}table{width:100%;border-collapse:collapse}table td,table th{vertical-align:middle;padding:3px 10px;border:1px solid var(--foreground);overflow:hidden}table td.action,table th.action{text-align:center}table th{text-align:left;font-weight:bold;background:var(--background)}toggle{display:block;overflow:hidden;text-align:center;background-color:var(--shade7);border:none;border-bottom:var(--borderLight);color:var(--fontColorMajor)}toggle label{padding:8px 0;width:50%;float:left;display:inline-block;text-align:center;cursor:pointer;position:relative;z-index:2;transition:background-color 0.2s ease-in-out;font-size:unset}toggle label:hover{background:var(--hoverMajor)}toggle input{width:32px;height:32px;order:1;z-index:2;position:absolute;right:30px;top:50%;transform:translateY(-50%);cursor:pointer;visibility:hidden}toggle input:checked+label{background-color:var(--colorMajor)}toolbar{display:block;padding:2px 8px;background-color:var(--foreground);text-align:right}#codegit+toolbar,.settings+toolbar{border-top:var(--borderLight)}a,a:link,a:visited{cursor:pointer}.hide{display:none}.clear{clear:both}.right{text-align:right}.left{text-align:left}.center{text-align:center}.italic{font-style:italic}.strong{font-weight:bold}pre{background:var(--shade7);padding:10px;margin:5px 0;font-family:'Ubuntu-Fira', monospace;overflow:auto}hr{margin:0}h1,h2,h3,h4{text-shadow:1px 1px 3px var(--shade9)}h1{font-size:20px}h2{font-size:20px}h3{font-size:18px}h4{font-size:16px}.ace_prompt_container{padding:0;box-shadow:none;margin:35px auto}.ace_prompt_container .ace_gotoLine{width:150px !important}.ace_prompt_container .ace_editor{position:relative !important}.ace_search{border-radius:0 !important;border:1px solid var(--shade2) !important;color:unset;background-color:var(--foreground);padding:5px;padding-top:28px}.ace_search .ace_search_form,.ace_search .ace_replace_form{margin:0;margin-bottom:5px;overflow:hidden;line-height:1.9}.ace_search .ace_search_form.ace_nomatch{outline:none}.ace_search .ace_nomatch .ace_search_field{background-color:var(--maroon)}.ace_search .ace_search_field{border-radius:0 !important;font-size:inherit;margin:0;line-height:inherit;padding:3px;width:15em;min-width:auto;vertical-align:middle;background-color:var(--shade7);border:none;border-bottom:var(--borderLight);color:var(--fontColorMajor)}.ace_search .ace_searchbtn{border-radius:0 !important;line-height:inherit;display:inline-block;padding:3px;cursor:pointer;margin:0;position:relative;width:4.5em;text-align:center;font-family:'Ubuntu', sans-serif;vertical-align:middle;background-color:var(--shade7);border:none;border-bottom:var(--borderLight);color:var(--fontColorMajor);border-left:var(--borderLight)}.ace_search .ace_searchbtn:hover{background:var(--hoverMajor)}.ace_search .ace_searchbtn:last-of-type{width:3em}.ace_search .ace_searchbtn.prev,.ace_search .ace_searchbtn.next{padding-bottom:8px;padding-top:9px;width:calc(4.5em / 2)}.ace_search .ace_searchbtn.prev::after,.ace_search .ace_searchbtn.next::after{display:none}.ace_search .ace_searchbtn.prev::before{content:"\f053"}.ace_search .ace_searchbtn.next::before{content:"\f054"}.ace_search .ace_searchbtn.prev,.ace_search .ace_searchbtn.next,.ace_search .ace_searchbtn_close{font-family:'Font Awesome 6 Free';font-weight:900;-webkit-font-smoothing:antialiased;display:inline-block;font-style:normal;font-variant:normal;text-rendering:auto;line-height:1}.ace_search .ace_searchbtn_close{background:none;color:var(--fontColorMajor);right:9px;position:absolute;top:6px}.ace_search .ace_searchbtn_close::before{content:"\f057"}.ace_search .ace_searchbtn_close:hover{background-color:#656565;background-position:50% 100%;color:white}.ace_search .ace_button{margin-left:0;margin-top:0 !important;box-sizing:border-box !important;display:inline-block;padding:2px;width:18px;height:18px;text-align:center;background-color:var(--shade7);border:none;border-bottom:var(--borderLight);color:var(--fontColorMajor)}.ace_search .ace_button:hover{background:var(--hoverMajor)}.ace_search .ace_button+.ace_button{margin-left:2px}.ace_search .ace_button:active{background-color:#ddd}.ace_search .ace_button.checked{border-color:var(--colorMajor);opacity:1}.ace_search .ace_search_options{margin-bottom:0;margin-right:25px;width:calc(100% - 35px);position:absolute;top:6px}.ace_search .ace_search_options .ace_button:first-of-type{font-family:'Ubuntu-Fira', monospace;color:var(--fontColorMajor);line-height:1.3}.ace_search .ace_search_counter{float:left;margin-top:2px;font-family:'Ubuntu-Fira', monospace}#ace_settingsmenu{border:1px solid var(--shade2) !important;color:unset;background-color:var(--foreground);box-shadow:none;right:14px;padding:5px}#ace_settingsmenu label{font-size:unset}#ace_settingsmenu table th,#ace_settingsmenu table td{padding:0 3px;border-color:var(--shade6)}#ace_settingsmenu table td[colspan="2"]{text-align:center;padding:0;border:none}#ace_settingsmenu button{margin:3px 0;background-color:var(--shade0);box-shadow:none;border:none}#ace_settingsmenu button+button,#ace_settingsmenu input+input{margin-left:6px}#ace_settingsmenu button[ace_selected_button=true]{background-color:var(--shade2)}#ace_settingsmenu input[type="checkbox"]{width:1.5rem;height:1.5rem;font-size:1.5rem;line-height:1.5rem}#ACTIVE{width:100%;background:var(--foreground);position:relative;transition:border 0.2s ease;grid-area:active}#tab_close,#tab_dropdown{position:absolute;top:8px;height:33px;transition:margin-right 0.2s ease-in-out;font-size:16px;cursor:pointer;color:var(--fontColorMajor)}#tab_close:hover,#tab_dropdown:hover{color:var(--colorMajor)}#tab_close{right:10px}#tab_dropdown{right:35px}ul.tabList li{position:relative;cursor:pointer;user-select:none;display:block;background-color:var(--colorSmall);padding:5px}ul.tabList li a{display:block;text-align:right;direction:rtl;color:var(--shade1);white-space:nowrap;text-shadow:1px 1px 3px rgba(0, 0, 0, 0.6);overflow:hidden}ul.tabList li a::before{content:'';width:100%;height:100%;position:absolute;top:0;left:0;background:linear-gradient(to left, transparent 90%, var(--colorSmall))}ul.tabList li a .subtle{color:var(--fontColorMinor);direction:ltr;unicode-bidi:embed}ul.tabList li .save,ul.tabList li .close{position:absolute;top:6px;background:inherit;width:20px;color:var(--fontColorMinor);opacity:0}ul.tabList li .save:hover,ul.tabList li .close:hover{color:var(--fontColorMajor)}ul.tabList li .save{right:22px}ul.tabList li .close{right:2px}ul.tabList li:hover{background-color:var(--colorMinor)}ul.tabList li:hover a::before{background:linear-gradient(to left, transparent 90%, var(--colorMinor))}ul.tabList li:hover .save,ul.tabList li:hover .close{opacity:1}ul.tabList li.changed{background-color:var(--colorContrast) !important}ul.tabList li.changed a::before{background:linear-gradient(to left, transparent 90%, var(--colorContrast)) !important}html.light ul.tabList li a{color:var(--shade7)}html.light ul.tabList li a .subtle{color:var(--shade6)}html.light ul.tabList .save,html.light ul.tabList .close{color:var(--shade6)}html.light ul.tabList .save:hover,html.light ul.tabList .close:hover{color:var(--shade9)}html.light #active_file_tabs li.active a{color:var(--shade9)}html.light #active_file_tabs li.active a .subtle{color:var(--shade6)}#active_file_dropdown{position:absolute;top:33px;right:15px;max-width:300px;z-index:9999;background-color:var(--foreground);border:var(--borderLight)}#active_file_dropdown li{margin:5px}#active_file_tabs{margin:0;padding:10px 0 0 18px;overflow:hidden;list-style-type:none;height:33px}#active_file_tabs li{float:left;margin-right:12px;z-index:2}#active_file_tabs li a{width:150px;margin-left:-5px;text-shadow:1px 1px 3px rgba(0, 0, 0, 0.6);padding-bottom:1px}#active_file_tabs li::before,#active_file_tabs li::after{display:block;content:" ";position:absolute;top:0;height:100%;width:44px;background-color:inherit;z-index:-1}#active_file_tabs li::before{right:-8px;transform:skew(30deg, 0deg);box-shadow:rgba(0, 0, 0, 0.1) 3px 2px 5px, inset rgba(255, 255, 255, 0.09) -1px 0}#active_file_tabs li::after{left:-10px;transform:skew(-30deg, 0deg);box-shadow:rgba(0, 0, 0, 0.1) -3px 2px 5px, inset rgba(255, 255, 255, 0.09) 1px 0}#active_file_tabs li.active{z-index:3;background-color:var(--colorMajor)}#active_file_tabs li.active a{color:var(--fontColorMajor)}#active_file_tabs li.active a .subtle{color:var(--fontColorMinor)}#active_file_tabs li.active a::before{background:linear-gradient(to left, transparent 90%, var(--colorMajor))}#codegit_repo_banner[style*='display: block']+#FILETREE{height:calc(100% - 22px)}#FILETREE .repo-icon{font-size:10px;line-height:20px;position:absolute;left:0;color:var(--background)}#project-root .repo-icon{font-size:12px;line-height:24px;position:absolute;left:2px;color:var(--background)}[id^="codegit_"].content,#codegit.content{width:800px}#dialog>.content{min-width:600px;overflow:hidden;overflow-y:scroll;height:400px;padding:5px}#dialog #codegit form{background-color:var(--background)}#codegit{display:flex;border-top:var(--borderLight)}#codegit_repo_banner{padding:4px 10px 2px 10px;cursor:pointer}.repoUnknown{background-color:var(--maroon)}.repoCommitted{background-color:var(--green)}.repoUncommitted{background-color:var(--red)}.repoUntracked{background-color:var(--blue)}#codegit_blame .file-info{background-color:var(--blue);font-size:16px;font-weight:bold;padding:5px}#codegit_blame ul{background-color:var(--background);overflow-x:auto}#codegit_blame li{font-family:'Ubuntu-Fira', monospace;position:relative;white-space:nowrap}#codegit_blame li span{line-height:18px;padding:2px 0}#codegit_blame li span.line{color:var(--fontColorMinor);white-space:pre;border-right:var(--borderLight)}#codegit_blame li span.code{white-space:pre}#codegit_blame li span.blame{display:none;position:absolute;top:-1px;width:100%;background-color:var(--background)}#codegit_blame li span.blame .date{color:var(--blue);float:right;padding:0}#codegit_blame li span.blame .author{color:var(--teal)}#codegit_blame li span.blame .hash{color:var(--red);white-space:pre;border-right:var(--borderLight)}#codegit_blame li:hover span.blame{display:block}#codegit_diff .file-info{background-color:var(--blue);font-size:16px;font-weight:bold;padding:5px}#codegit_diff ul{background-color:var(--background);padding:5px;overflow-x:auto}#codegit_diff li{color:var(--fontColorMinor);font-family:'Ubuntu-Fira', monospace;white-space:pre}#codegit_diff li span{line-height:18px;margin-left:5px;padding:2px 10px 2px 0;border-left:var(--borderLight)}#codegit_diff li.wrapper{text-align:center;color:var(--teal)}#codegit_diff li.addition{color:var(--green)}#codegit_diff li.deletion{color:var(--red)}#codegit_diff li.context span{color:var(--fontColorMajor)}#commit_message{display:inline-block;width:calc(100% - 185px)}#codegit_overview table{width:100%}#codegit_overview tbody{display:block;max-height:410px;overflow-y:scroll}#codegit_overview thead,#codegit_overview tbody tr{display:table;width:100%;table-layout:fixed}#codegit_overview thead{width:calc(100% - 10px)}#codegit_overview table th:nth-of-type(1),#codegit_overview table td:nth-of-type(1){width:40px}#codegit_overview table th:nth-of-type(2),#codegit_overview table td:nth-of-type(2){width:80px}#codegit_overview table th:nth-of-type(4),#codegit_overview table td:nth-of-type(4){width:195px;text-align:center;padding:0}#codegit_log .commit{padding:8px 5px;border-bottom:var(--borderLight)}#codegit_log .commit p{line-height:18px}#codegit_log .hash{display:inline;color:var(--fontColorMinor)}#codegit_log .hash a,#codegit_log .hash span{font-family:'Ubuntu-Fira', monospace;color:var(--jade)}#codegit_log .date{color:var(--fontColorMinor);user-select:none;float:right}#codegit_log .author{display:block;color:var(--fontColorMinor)}#codegit_log .author span{color:var(--blue)}#codegit_log .message{color:var(--fontColorMajor);margin:5px 0}.git_area header input{display:inline}.git_area{max-height:45vh;overflow-y:scroll}.git_area input[type="checkbox"]{margin:auto}.git_area .right{position:relative;left:5px;top:15px}.git_area .modified,.git_area .untracked,.git_area .deleted{color:red}.git_area .added,.git_area .renamed{color:green}.git_blame_area .content{max-height:40vh;overflow-y:auto}.git_blame_area table td:nth-of-type(1){width:250px}.git_blame_area table td:nth-of-type(2){width:90px}.git_blame_area table td:nth-of-type(3){max-width:500px;overflow:hidden;text-overflow:ellipsis;vertical-align:middle;white-space:nowrap}.git_clone_area table td,.git_clone_area table th{border:none}.git_push_area table td,.git_push_area table th{border:none;padding:0}.git-stat{color:#ff982d;color:rgba(255, 152, 45, 0.7)}#git-stat{float:left}#FILETREE span.uncommit:before{content:"\25cf";font-size:1.9em}#FILETREE span.uncommit{float:left;padding-top:8px}#EDITOR{width:auto;height:100%;transition:margin 0.2s ease-in-out;grid-area:editor}.editor{width:100%;height:100%;position:absolute;top:0;left:0;right:0;bottom:0}.ace_content{padding:0;margin:0}.ace_gutter{z-index:1}.ace_gutter-layer{padding:0;background-image:linear-gradient(135deg, var(--mideground) 8.33%, transparent 8.33%, transparent 50%, var(--mideground) 50%, var(--mideground) 58.33%, transparent 58.33%, transparent 100%);background-size:6px 6px;color:var(--fontColorMinor)}.ace_scrollbar{z-index:1}.ace_error{background-position:3px 0 !important}#ROOTEDITORWRAPPER{display:flex;height:100%;position:relative}#EDITOR,.editorWindow{display:flex}#EDITOR.horizontal,.editorWindow.horizontal{flex-direction:row}#EDITOR.vertical,.editorWindow.vertical{flex-direction:column}.editorWindow,.editorPane{flex:1 1 auto;overflow:hidden;position:relative}#BOTTOM{position:relative;z-index:1;height:23px;padding:3px 0;background:var(--foreground);color:var(--fontColorMinor);grid-area:bottom}#BOTTOM>a,#BOTTOM>span{font-size:12px;user-select:none;display:inline-block;padding:0 10px;line-height:16px;border-left:1px solid var(--fontColorMinor)}#BOTTOM>a:first-child,#BOTTOM>span:first-child{border-left:none}#BOTTOM>a:hover{color:#fff}#cursor-position{position:absolute;right:30px;bottom:4px;font-size:12px;z-index:99;color:#999}#split_menu{bottom:23px;left:5px}#changemode_menu{bottom:23px;left:45px;font-size:inherit}.splitter{z-index:9999;background-color:var(--mideground);flex:0 0 10px;width:100%;height:100%}.splitter.horizontal{cursor:col-resize}.splitter.vertical{cursor:row-resize}overlay{display:none;position:fixed;top:0;left:0;width:100%;height:100%;background-color:rgba(0, 0, 0, 0.6);z-index:99990;opacity:0}overlay.active{display:block;opacity:1}#alert .drag,#dialog .drag,#alert .close,#dialog .close{position:absolute;font-size:20px;padding:0;color:var(--fontColorMinor);top:5px}#alert .drag,#dialog .drag{right:28px;cursor:grab}#alert .drag.active,#dialog .drag.active{cursor:grabbing}#alert .close,#dialog .close{right:5px;padding:0;cursor:pointer}toaster.top,output.top{top:20px}toaster.bottom,output.bottom{bottom:30px}toaster.left,output.left{left:20px}toaster.center,output.center{left:50%;margin-left:-140px}toaster.right,output.right{right:20px}.alert{display:none;min-height:50px;min-width:200px;position:absolute;top:10%;left:calc(50% - 200px);background:var(--mideground);border:3px solid rgba(255, 255, 255, 0.5);padding:10px;z-index:99999}.alert h1{font-size:18px;font-weight:bold;text-align:center;margin-bottom:5px}.alert h2{text-align:left;font-size:14px}.alert .actions{width:100%;margin-top:5px;text-align:center}.alert .actions button{min-width:90px}#dialog{position:absolute;top:15%;left:50%;max-height:70vh;max-width:90vw;z-index:99995;overflow-y:auto;min-height:100px;min-width:400px;border:var(--borderHeavy)}#content{background-color:var(--background)}#content form{padding:10px 10px 0;background-color:var(--mideground)}#content form toolbar{margin:10px -10px 0}output{min-width:480px;max-width:720px;z-index:99999;position:fixed;text-align:left !important}output result{background:var(--mideground);font-size:0.9rem;border:var(--borderLight);display:block;margin:5px;transition:opacity ease-in-out 300ms;opacity:0;cursor:pointer}output result.active{opacity:1}output result pre{margin:0;border-left:5px solid}output result pre.success{border-color:var(--green)}output result pre.notice{border-color:var(--cyan)}output result pre.warning{border-color:var(--orange)}output result pre.error{border-color:var(--red)}toaster{min-width:200px;max-width:480px;z-index:99998;position:fixed;text-align:left !important}toaster toast{background:var(--mideground);padding:5px;font-size:0.9rem;border:var(--borderLight);display:block;margin:5px;transition:opacity ease-in-out 300ms;opacity:0;cursor:pointer}toaster toast.active{opacity:1}toaster toast .message{text-align:left;margin:0 5px;display:inline-block}toaster toast i.success{color:var(--green)}toaster toast i.notice{color:var(--cyan)}toaster toast i.warning{color:var(--orange)}toaster toast i.error{color:var(--red)}#FILETREE{width:100%;height:100%;float:left;margin:0;overflow-y:scroll}#FILETREE #project-root+ul{padding-left:5px}#FILETREE ul{margin:0;padding:0;padding-left:4px;transition:height 500ms ease-in;overflow:hidden}#FILETREE li{display:block;margin:0;list-style:none}#FILETREE ul ul li{margin-left:22px;white-space:nowrap}#FILETREE a{display:inline-block;min-width:100%;cursor:pointer;padding:2px 5px;padding-left:0px;z-index:80;position:relative}#FILETREE a:hover,#FILETREE a.context-menu-active{background:var(--hoverMinor)}#FILETREE i{margin:0 3px;font-size:18px;display:inline-block;height:18px;width:18px;text-align:center}#FILETREE i:before{text-align:center;line-height:18px}#FILETREE i.expand:not(.none){margin-left:-22px;height:auto;width:auto}#FILETREE i.none{display:none}#FILETREE i.root{font-size:22px;margin-left:3px;width:auto;height:auto}#FILETREE span{vertical-align:text-top}#FILETREE .fa-folder{color:var(--colorMajor)}#FILETREE .fa-plus,#FILETREE .fa-minus{color:gray}#FILETREE .loading{animation-name:spin;animation-duration:2000ms;animation-iteration-count:infinite;animation-timing-function:linear}.sidebar{position:relative;top:0;height:100%;z-index:99990;-moz-user-select:none;-khtml-user-select:none;-webkit-user-select:none;user-select:none;background-color:var(--background);transition:right 0.2s ease-in-out, left 0.2s ease-in-out;white-space:nowrap}.sidebar.unlocked{grid-area:none;position:fixed}#SBLEFT{width:350px;left:0;grid-area:leftsb}#SBRIGHT{width:250px;right:0;grid-area:rightsb}#SBRIGHT .category{font-size:18px;font-weight:600;color:var(--colorMajor);padding-bottom:5px;margin-left:5px}#SBRIGHT a{margin-left:5px;display:block;min-width:100%;padding:5px}#SBRIGHT a:hover{background:var(--hoverMinor)}#SBRIGHT hr{height:0;border:none;border-top:var(--borderLight);padding:0;margin:10px 0}@media (max-width:576px){#workspace #SBLEFT{width:80vw}#workspace #SBRIGHT{width:80vw}}.sidebar .content{position:absolute;top:33px;width:calc(100% - 15px);overflow-y:auto}#SBLEFT .content{left:0;right:15px;bottom:280px}#SBLEFT>.content{transition:bottom ease-in-out 0.2s}#SBRIGHT .content{left:15px;right:0;height:calc(100% - 33px)}#SBRIGHT #last_login{display:block;margin-bottom:5px}.handle{width:15px;height:100%;z-index:99999;margin:0;padding:0;background:var(--mideground);cursor:col-resize}.handle span{display:block;position:absolute;width:11px;height:70px;top:calc(50% - 35px);line-height:66px;font-size:20px;text-align:center;letter-spacing:-2px;color:var(--fontColorMinor)}.sidebar.unlocked .handle span{width:30px;background:var(--mideground);z-index:10;letter-spacing:0}.sidebar.unlocked .handle span::before,.sidebar.unlocked .handle span::after{content:'';height:20px;width:18px;position:absolute;background-color:var(--mideground)}.sidebar.unlocked .handle span::before{top:-6px;border-top:var(--borderLight)}.sidebar.unlocked .handle span::after{bottom:-6px;border-bottom:var(--borderLight)}#SBLEFT .handle{position:absolute;right:0;border-right:var(--borderLight)}#SBLEFT.unlocked .handle span{margin-left:3px;border-right:var(--borderLight)}#SBLEFT.unlocked .handle span::before,#SBLEFT.unlocked .handle span::after{right:-2px;border-right:var(--borderLight)}#SBLEFT.unlocked .handle span::before{transform:skew(0deg, 30deg)}#SBLEFT.unlocked .handle span::after{transform:skew(0deg, -30deg)}#SBRIGHT .handle{border-left:var(--borderLight)}#SBRIGHT.unlocked .handle span{margin-left:-20px;border-left:var(--borderLight)}#SBRIGHT.unlocked .handle span::before,#SBRIGHT.unlocked .handle span::after{left:-2px;border-left:var(--borderLight)}#SBRIGHT.unlocked .handle span::before{transform:skew(0deg, -30deg)}#SBRIGHT.unlocked .handle span::after{transform:skew(0deg, 30deg)}#SBLEFT #project_list{position:absolute;height:280px;bottom:0;left:0;right:15px;transition:height ease-in-out 0.2s;overflow:hidden}#SBLEFT #project_list .title{right:0}#SBLEFT #project_list .content{height:calc(100% - 33px);overflow-y:scroll;width:100%}#SBLEFT #project_list .content i{margin-right:5px;color:var(--colorMajor)}#SBLEFT #project_list .content ul{height:100%;display:block}#SBLEFT #project_list .content li{padding:5px;white-space:nowrap;cursor:pointer}#SBLEFT #project_list .content li:hover{background-color:var(--shade2)}.sidebar .title{position:absolute;top:0;height:33px;overflow:hidden;background-color:var(--foreground);z-index:9998}.sidebar .title h2{font-size:15px;margin:8px;display:inline-block}.sidebar .title i{display:block;font-size:15px;color:var(--fontColorMajor);z-index:99999;cursor:pointer;margin:9px 4px;float:left}.sidebar .title i.active,.sidebar .title i:hover{color:var(--colorMajor)}#SBRIGHT .title{right:0;left:15px}#SBRIGHT .title i{float:left}#SBLEFT .title{right:15px;left:0}#SBLEFT .title i{float:right}#textmode tbody td{width:50%;padding:0;padding-left:8px;padding-right:8px}#macro_editor{height:450px;overflow-y:auto;overflow-x:hidden}#macro_editor tbody{display:block;max-height:49vh;overflow-y:scroll}#macro_editor thead,#macro_editor tbody tr{display:table;table-layout:fixed}#macro_editor td{padding:0}#macro_editor td input,#macro_editor td toggle{margin:0;border-bottom:none}#macro_editor td input:focus,#macro_editor td toggle:focus{border-bottom:none}#macro_editor th:nth-of-type(1),#macro_editor td:nth-of-type(1){width:150px;font-weight:700}#macro_editor th:nth-of-type(2),#macro_editor td:nth-of-type(2){width:150px}#macro_editor th:nth-of-type(3),#macro_editor td:nth-of-type(3){width:100px}#macro_editor th:nth-of-type(4),#macro_editor td:nth-of-type(4){width:300px}#macro_editor th:nth-of-type(5),#macro_editor td:nth-of-type(5){font-size:18px;width:100px;text-align:center}#macro_editor th:nth-of-type(5) a,#macro_editor td:nth-of-type(5) a{margin:0 5px}#macro_editor th:nth-of-type(5) .fa-save,#macro_editor td:nth-of-type(5) .fa-save{color:var(--green)}#macro_editor th:nth-of-type(5) .fa-times-circle,#macro_editor td:nth-of-type(5) .fa-times-circle{color:var(--red)}#market{width:960px}#market table th{font-size:18px;background-color:#333}#manual_repo{display:inline-block;width:80%;margin:0}#manual_install{display:inline-block;width:19%;margin:0}#market_table{height:450px;overflow-y:auto;overflow-x:hidden}#market_table tbody{display:block;max-height:49vh;overflow-y:scroll}#market_table thead,#market_table tbody tr{display:table;width:100%;table-layout:fixed}#market_table th[colspan="4"],#market_table td[colspan="4"]{padding:5px 10px;background-color:var(--shade7)}#market_table th:nth-of-type(1),#market_table td:nth-of-type(1){width:200px;font-weight:700}#market_table th:nth-of-type(2),#market_table td:nth-of-type(2){width:500px}#market_table th:nth-of-type(4),#market_table td:nth-of-type(4){width:100px;text-align:center}#market_table td:nth-of-type(4){font-size:18px}#market_table td:nth-of-type(4) a{margin:0 5px}#market_table td:nth-of-type(4) .fa-check-circle{color:var(--green)}#market_table td:nth-of-type(4) .fa-times-circle{color:var(--red)}#market_table td:nth-of-type(4) .fa-sync-alt,#market_table td:nth-of-type(4) .fa-plus-circle{color:var(--blue)}i{width:1.25em;text-align:center}.merged-icon{float:right;position:relative;top:13px;right:24px;height:0;width:0;cursor:pointer}a i,h1 i,h2 i,h3 i,h4 i,hint i,span i,label i{margin-right:5px}.loader{width:100%;height:100%;position:relative}.loader h2{text-align:center;width:100%;position:absolute;top:calc(50% - 60px)}.loader h2 em{display:inline-block;animation:jump 1500ms infinite ease-in-out}.loader h2 em:nth-of-type(0){animation-delay:500ms}.loader h2 em:nth-of-type(1){animation-delay:700ms}.loader h2 em:nth-of-type(2){animation-delay:900ms}.loader h2 em:nth-of-type(3){animation-delay:1100ms}.loader h2 em:nth-of-type(4){animation-delay:1300ms}.loader h2 em:nth-of-type(5){animation-delay:1500ms}.loader h2 em:nth-of-type(6){animation-delay:1700ms}.loader h2 em:nth-of-type(7){animation-delay:1900ms}.loader h2 em:nth-of-type(8){animation-delay:2100ms}.loader h2 em:nth-of-type(9){animation-delay:2300ms}.loader h2 em:nth-of-type(10){animation-delay:2500ms}.loader h2 em:nth-of-type(11){animation-delay:2700ms}.loader h2 em:nth-of-type(12){animation-delay:2900ms}.loader h2 em:nth-of-type(13){animation-delay:3100ms}.loader h2 em:nth-of-type(14){animation-delay:3300ms}.loader h2 em:nth-of-type(15){animation-delay:3500ms}.loader h2 em:nth-of-type(16){animation-delay:3700ms}.loader h2 em:nth-of-type(17){animation-delay:3900ms}.loader h2 em:nth-of-type(18){animation-delay:4100ms}.loader h2 em:nth-of-type(19){animation-delay:4300ms}.loader h2 em:nth-of-type(20){animation-delay:4500ms}.loader h2 em:nth-of-type(21){animation-delay:4700ms}.loader h2 em:nth-of-type(22){animation-delay:4900ms}.loader h2 em:nth-of-type(23){animation-delay:5100ms}.loader .dual-ring{display:inline-block;position:absolute;top:calc(50% - 20px);left:calc(50% - 20px);width:40px;height:40px}.loader .dual-ring::before,.loader .dual-ring::after{content:" ";display:block;width:100%;height:100%;margin:-6px;border-radius:50%;position:absolute}.loader .dual-ring::before{border:6px solid var(--blue);opacity:0.2;animation:glow 3s linear infinite}.loader .dual-ring::after{border:6px solid var(--blue);border-color:var(--blue) transparent var(--blue) transparent;animation:spin 1.5s linear infinite}@keyframes glow{0%{opacity:0.2}30%{opacity:0.4}50%{opacity:0.8}70%{opacity:0.4}100%{opacity:0.2}}@keyframes spin{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes jump{0%{transform:translateY(0)}50%{transform:translateY(1px)}100%{transform:translateY(0)}}#logo{position:fixed;top:calc(25% - 200px);left:calc(20% - 140px);opacity:1;z-index:1}#logo svg path{animation:float 1.5s infinite ease-in-out alternate;fill-opacity:0;stroke:var(--colorMajor);stroke-width:15}#logo svg #alpha{stroke:#F5F9F9}#logo svg #backdrop{fill-opacity:1;fill:var(--colorSmall);stroke:none}#logo svg .delay-1{animation-delay:0.1s}#logo svg .delay-2{animation-delay:0.2s}#logo svg .delay-3{animation-delay:0.3s}#logo svg .delay-4{animation-delay:0.4s}@keyframes float{100%{transform:translateY(10px)}}.project-delete{background-color:var(--shade7)}.project-delete tr td:first-child{font-weight:bold}.project-delete tr td+td{padding:0}.project-delete tr td+td pre{margin:0}.project-delete tr toggle{border-bottom:none}#contextmenu{display:none;position:fixed;width:200px;background:var(--mideground);z-index:9999999;border:var(--borderLight);box-shadow:0px 0px 10px 0px rgba(0, 0, 0, 0.9);overflow-y:auto}#contextmenu a{display:block;padding:5px 5px 7px 5px;margin:0;user-select:none}#contextmenu a:hover{background:var(--hoverMinor)}#contextmenu hr{height:0;border:none;border-top:1px solid #666;margin:3px}#contextmenu .disabled{color:#999;font-style:italic}#probe_results{height:250px;overflow:scroll !important;border:1px solid #262626;padding:0;max-width:800px}#probe_results div{padding:4px 6px;overflow:hidden}#probe_results div strong{font-weight:700;color:var(--blue)}#probe_results div a{display:block;white-space:nowrap}#probe_results div a span:nth-child(1){padding:4px 6px 3px 6px;color:var(--teal);display:inline-block}#probe_results div a:hover{background-color:#1a1a1a}#probe_processing{display:none}#probe_table tr td{border:none;padding:0}#filter_wrapper{position:absolute;z-index:9999;top:0;left:0;bottom:0;right:0;background-color:var(--foreground);display:none}#finder-inner-wrapper{left:0;right:30px;top:5px;bottom:3px;position:absolute}#filter_input{display:inline-block;width:calc(100% - 65px);margin:2px 5px}#filter_strategy{top:35px;right:0}#dialog form.transfer{padding:5px 10px 10px}#upload_wrapper{position:relative;display:block;text-align:center;text-transform:uppercase;padding:50px;border:2px dashed #666;font-size:25px;color:var(--fontColorMinor);background-color:var(--shade7);cursor:pointer}#upload_wrapper.hover{color:var(--fontColorMajor)}.upload-progress{margin:5px auto 0;background-color:var(--shade7);position:relative;height:20px;border:var(--borderLight)}.upload-progress span,.upload-progress div{display:block;position:absolute;top:0;left:0;height:100%;font-family:'Ubuntu-Fira'}.upload-progress span{padding:1px;bottom:0;right:0;text-align:center;vertical-align:middle}.upload-progress div{background-color:var(--green)}.upload-progress div.error{background-color:var(--red)}#login,#error{left:calc(50% - (384px / 2));z-index:1;top:35%;position:absolute}#login fieldset,#error fieldset{width:384px;border:3px solid var(--colorMajor);background:var(--background);margin:auto;padding:15px 35px 25px 35px}#login legend,#error legend{font-size:32px;font-weight:700;text-align:center}#login legend span,#error legend span{font-size:1rem}#login label,#error label{font-size:14px;display:inline-block;vertical-align:super}#login input[type="password"],#error input[type="password"],#login input[type="text"],#error input[type="text"]{margin-top:0;margin-bottom:8px}#login #login_options,#error #login_options{position:relative;display:none}#login button,#error button{float:right}#login button+button,#error button+button{margin-right:6px}#login .merged-icon,#error .merged-icon{top:8px}#github_link{position:absolute;bottom:2px;right:3px;color:var(--colorMajor);font-weight:bold;text-decoration:none}#myth{left:-20px;position:fixed;top:-20px;pointer-events:none;min-height:120vh;min-width:120vw;background-color:#000;background:linear-gradient(-45deg, var(--background) 45%, var(--colorMajor) 50%, var(--background) 55%);background-size:600% 600%;animation:gradient 16s linear infinite}@keyframes gradient{0%{background-position:0% 51%}50%{background-position:100% 50%}100%{background-position:0% 51%}}#installer{position:absolute;width:450px;top:15%;left:50%;margin-left:-175px;padding:35px;overflow:auto;z-index:1}#installer h1{font-size:24px;margin:0 0 15px}#installer table{margin:5px 0;background-color:var(--background)}#installer table label{margin:0}#installer table td{width:50%;border:var(--borderLight);padding:0}#installer table td:first-child{padding-left:8px;font-weight:bold}#installer table td:not(:first-child){text-align:center}#installer table td toggle,#installer table td select{border-bottom:none}#installer table select{margin:0;padding:7px 3px}#installer.errors label{margin-top:1rem;margin-bottom:-5px}.install_issues{background:var(--shade7);padding:10px;margin:10px 0 5px;font-family:'Ubuntu-Fira' monospace;overflow:auto}.install_issues p{padding:0;margin:0}.install_issues p i{margin-right:3px}.install_issues .success{color:green}.install_issues .error{color:red}#update_changes{white-space:pre-wrap;overflow:auto;max-height:200px;max-width:510px}.settings .analytics td:nth-child(odd){padding-left:20px;font-weight:bold;text-align:left}.settings .analytics td:nth-child(even){font-family:'Ubuntu-Fira', monospace}.settings .analytics p{font:normal 14px 'Ubuntu', sans-serif}.settings .analytics label{font:normal 14px 'Ubuntu', sans-serif}.settings .keybindings td:nth-child(odd){padding-left:20px;font-weight:bold;text-align:left}.settings .keybindings td:nth-child(even){font-family:'Ubuntu-Fira', monospace}.settings i+i{margin-left:5px}.settings .key,.settings .cmd{color:var(--background);background-color:var(--fontColorMajor);text-align:center;font-weight:900;display:inline-block;text-transform:uppercase}.settings .cmd{padding:2px;width:35px;border-radius:3px}.settings .key{border-radius:50%;width:1em}.settings{display:flex;border-top:var(--borderLight)}.settings table{margin:0 5px;width:calc(100% - 10px)}.settings table td{border:none;border-bottom:1px solid #666;padding:8px 0;position:relative;overflow:unset}.settings table td:first-child{padding-left:20px;font-weight:bold}.settings table td:not(:first-child){text-align:center}.settings table select{margin:0}.settings table tr:last-child td{border-top:none}#projectSelect{max-height:250px;margin:10px 0 5px 0;overflow:auto}.user i{font-size:1.5rem;vertical-align:middle;cursor:pointer}.user i:hover{color:var(--colorMajor)}#content .uploadBulk{padding:0;display:inline-block;position:relative;background-color:transparent}#content .uploadBulk input{height:100%;position:absolute;pointer-events:none;border:none;padding:0;margin:0;right:0;top:0;opacity:0}body,html{width:100%;height:100%;color:var(--shade0);background-color:var(--background);font:normal 14px 'Ubuntu', sans-serif}#workspace{background-color:var(--background);margin:0;padding:0;width:100%;height:100%;z-index:1;overflow:hidden;display:grid;grid-template-rows:auto 1fr auto;grid-template-columns:auto 1fr auto;grid-template-areas:"leftsb active rightsb" "leftsb editor rightsb" "leftsb bottom rightsb"}#download{display:none}