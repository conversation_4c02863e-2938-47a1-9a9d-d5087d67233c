#login,
#error {
	left: calc(50% - (384px / 2));
	// padding: 35px;
	z-index: 1;
	top: 35%;
	position: absolute;
	// background: rgba($base00, 0.8);

	fieldset {
		width: 384px;

		border: 3px solid $colorMajor;
		background: $background;
		margin: auto;
		// margin-top: 15px;
		// width: 310px;
		padding: 15px 35px 25px 35px;
	}

	legend {
		font-size: 32px;
		font-weight: 700;
		text-align: center;

		span {
			font-size: 1rem;
		}
	}

	label {
		font-size: 14px;
		display: inline-block;
		vertical-align: super;
	}

	input[type="password"],
	input[type="text"] {
		margin-top: 0;
		margin-bottom: 8px;
	}

	#login_options {
		position: relative;
		display: none;
	}

	button {
		float: right;
	}

	button+button {
		margin-right: 6px;
	}

	.merged-icon {
		top: 8px;
	}
}

#github_link {
	position: absolute;
	bottom: 2px;
	right: 3px;
	color: $colorMajor;
	font-weight: bold;
	text-decoration: none;
}