{"deny": "Non", "affirm": "O<PERSON>", "enabled": "Activé", "disabled": "Désactivé", "on": "Activé", "off": "Désactivé", "true": "Vrai", "false": "Faux", "passed": "SUCCES", "failed": "ECHEC", "show": "<PERSON><PERSON>", "hide": "<PERSON><PERSON>", "login": {"": "Connexion", "last": "Dernière connexion : %s"}, "logout": "Déconnexion", "rememberMe": "Se souvenir de moi", "find": "Trouver", "find:": "Trouver :", "replace": {"": "<PERSON><PERSON>lace<PERSON>", "all": "<PERSON><PERSON><PERSON><PERSON>"}, "replace:": "Remplacer :", "gotoLine": "<PERSON>er à la ligne", "moveLine": {"up": "<PERSON><PERSON><PERSON>r la ligne vers le haut", "down": "<PERSON><PERSON><PERSON>r la ligne vers le bas"}, "cycleActive": {"next": "Vers le fichier suivant", "prev": "Vers le fichier précédent"}, "key": {"alt": "Alt", "esc": "Esc", "ctrl": "Ctrl", "shift": "Shift"}, "cut": "Couper", "copy": "<PERSON><PERSON><PERSON>", "paste": "<PERSON><PERSON>", "extract": {"": "Extraire", "success": "Extraction de l'archive effectuée.", "unable": "Impossible d'extraire l'archive.", "noOpen": "Impossible d'ouvrir l'archive.", "noZip": "ZipArchive (PHP) n'est pas installé ou activé.", "noPhar": "PharData (PHP) n'est pas installé ou activé.", "noRar": "<PERSON><PERSON> (PHP) n'est pas installé ou activé.", "noDecomp": "Impossible d'extraire l'archive gzip.", "unrecognized": "Type d'archive non supporté."}, "duplicate": {"": "<PERSON><PERSON><PERSON><PERSON>", "name": "Entrer un nouveau nom :", "file": "<PERSON><PERSON><PERSON> en double", "folder": "Dossier en double"}, "preview": "Prévisualisation", "outsideWorkspace": "Fichier en dehors de l'espace de travail.", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON>", "name": "Nom", "description": "Description", "author": "<PERSON><PERSON><PERSON>", "actions": "Actions", "email": "Email", "create": {"": "<PERSON><PERSON><PERSON>", "type": "Créer %s"}, "rename": {"": "<PERSON>mmer", "type": "Renommer %s"}, "delete": {"": "<PERSON><PERSON><PERSON><PERSON>", "type": "Supprimer %s", "scope": "Supprimer les fichiers sur le serveur ?"}, "edit": "Editer", "rescan": "<PERSON><PERSON><PERSON> le <PERSON>", "download": "Télécharger", "save": {"": "<PERSON><PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON><PERSON> tout", "active": "Sauvegarder le fichier actif"}, "open": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "close": {"": "<PERSON><PERSON><PERSON>", "all": "<PERSON>rm<PERSON> tout", "active": "<PERSON><PERSON><PERSON> le fichier actif"}, "closeUnsavedFile": "Fermer le fichier non sauvegardé ?", "save&close": "Sauvegarder & Fermer", "discardChanges": "Annuler les modifications", "ln": "<PERSON>g", "in": "Dans:", "all": "Tous", "col": "Col", "help": "Aide", "mode": "Mode", "more": "Plus", "less": "<PERSON>ins", "users": "Utilisateurs", "search": "<PERSON><PERSON><PERSON>", "filterTree": "Filtrer l'arborescense", "prefix": "Préfixe", "substring": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "regex": "Regex", "scout": {"open": "Ouvrir Scout", "query": "Requête :", "searching": "Recherche...", "searchFiles": "Rechercher des fichiers :", "fileTypes": "Types séparés par des espaces", "withinWorkSpace": "Dans l'espace de travail"}, "noShell": "La commande Shell_exec() n'est pas activée", "themes": "Thèmes", "branch": "Branche", "editor": "Editeur", "extension": "Extension", "extensions": "Extensions", "textmode": "Mode texte", "textmodes": "Modes texte", "saveExtensions": "Sauvegarder extensions", "extensionSet": "%s est déjà défini", "keybindings": "<PERSON><PERSON><PERSON><PERSON> clav<PERSON>", "plugins": "Plugins", "system": "Système", "administration": "Administration", "wrap": {"": "Retour à la ligne", "on": "Retour à la ligne activé", "off": "Retour à la ligne désactivé"}, "confirm": "Confirmer", "explore": "Explorer", "continue": "<PERSON><PERSON><PERSON>", "analytics": {"": "Analyses d'usage", "enabled": "Analyses d'usage anonymes", "send": "Envoyer les analyses d'usage", "policy": "Nous collectons les informations ci-dessus lors de l'utilisation de l'IDE Atheos. Nous ne partagerons vos données, elles sont destinées à comprendre les besoins des utilisateurs et à améliorer l'IDE Atheos. En optant pour notre programme d'analyses d'usage, vous acceptez la collecte et l'utilisation des informations conformément à cette politique. Plus d'informations peuvent être trouvées sur [racine]/docs/PRIVACY.md sur le dépôt Github."}, "uuid": "UUID", "version": "Version", "first_heard": "Premier envoi", "last_heard": "<PERSON><PERSON> envoi", "server_os": "Ser<PERSON><PERSON>", "client_os": "Navigateur", "timezone": "<PERSON><PERSON> ho<PERSON>", "settings": {"": "Paramètres", "autosave": "Les paramètres sont sauvegardés automatiquement.", "editor": "Paramètres de l'éditeur", "feedback": "Paramètres de notification", "draft": "Paramètres de brouillon", "system": "Paramètres système", "codegit": "Paramètres CodeGit"}, "feedback": "Notifications", "toast": {"location": "Emplacement des bulles d'infos :", "stay": {"success": "Temps d'affichage des messages de réussite :", "notice": "Temps d'affichage des messages d'information :", "warning": "Temps d'affichage des messages d'attention :", "error": "Temps d'affichage des messages d'échec :"}}, "output": {"location": "Emplacement des messages :", "stay": {"success": "Temps d'affichage des messages de réussite :", "notice": "Temps d'affichage des messages d'information :", "warning": "Temps d'affichage des messages d'attention :", "error": "Temps d'affichage des messages d'échec :"}}, "location": {"bl": "En bas à gauche", "bc": "En bas au centre", "br": "En bas à droite", "tl": "En haut à gauche", "tc": "En haut au centre", "tr": "En haut à droite"}, "manual": "Désactiver la fermeture automatique", "draft": {"": "Brouillon", "enable": "Activer la sauvegarde automatique sur brouillon", "interval": "Intervalle entre les sauvegardes automatiques", "verbose": "Informer de la sauvegarde automatique"}, "account": {"": "Compte utilisateur", "new": "Nouveau compte utilisateur", "create": "<PERSON><PERSON><PERSON> le compte utilisateur", "delete": "Supprimer le compte utilisateur", "confirm": "Confirmer la suppression du compte utilisateur", "download": "Télécharger le modèle d'insertion en masse", "upload": "<PERSON><PERSON><PERSON><PERSON>r un fichier de comptes utilisateur en masse"}, "account:": "Compte utilisateur :", "username": {"": "Utilisa<PERSON>ur", "new": "Nouvel utilisateur", "create": "C<PERSON>er un utilisateur"}, "username:": "Utilisateur :", "password": {"": "Mot de passe", "new": "Nouveau mot de passe", "change": "Changer le mot de passe", "changeUser": "Changer le mot de passe de %s", "confirm": "Confirmer le mot de passe"}, "password:": "Mot de passe :", "project": {"": "Projet", "current": "Projet courant", "noActive": "Pas de projet courant", "create": "Créer un projet", "exists": {"path": "Le répertoire du projet existe déjà.", "name": "Le nom du projet existe déjà."}, "unableCreate": "Impossible de créer le répertoire.", "unableAbsolute": "Impossible de créer le répertoire absolu.", "unablePermissions": "Aucune permission en lecture/écriture.", "new": "Nouveau projet", "list": "Liste des projets", "name": "Nom du projet", "rename": "Renommer le projet", "unableRename": "Cannot rename path.", "delete": "Suppresion du projet", "confirm": "Confirmez la <PERSON> du projet", "loaded": "%s est chargé", "missing": "Projet non trouvé", "gitnote": "<PERSON>la ne fonctionnera que si votre dépot Git ne nécessite pas d'authentification et si Git est installé sur votre serveur."}, "project:": "Projet :", "projects": "Projets", "projects:": "Projets :", "editUserACL": "Editer la liste de contrôle d'accès de %s", "workspaceProjects": "Espace de travail des projets", "accessAllProjects": "Accéder à TOUS les projets", "onlySelectedProjects": "Uniquement les projets sélectionnés", "update": {"": "Mise à jour", "check": "Vérification des mises à jour", "changes": "Evolution sur Atheos :", "current": "Votre système est à jour."}, "install": "Installer", "domain": "Domaine <PERSON>", "nightly": "Note : Votre installation est une 'nightly build'. Atheos pourrait être instable.", "yourVersion": "Votre version", "latestVersion": "Dernière version", "downloadAtheos": "Télécharger Atheos", "language": "<PERSON><PERSON>", "collapse": "Collapse", "enableLigatures": "Activer les ligatures de code", "highlightActiveLine": "Mettre la ligne active en surbrillance", "fontSize": "<PERSON><PERSON>", "pixel": {"": "%spx", "default": "%spx (défaut)"}, "foldWidgets": "Outil de pliage / dépliage du code ?", "tabs": {"soft": "Espaces en tabulations ?", "size": "<PERSON><PERSON> de la tabulation"}, "userList": "Liste des utilisateurs", "folder": {"": "Dossier", "invalid": "Do<PERSON>r invalide"}, "directory": {"": "Répertoire", "invalid": "Répertoire invalide"}, "file": {"": "<PERSON><PERSON><PERSON>", "new": "Nouveau fichier", "type": "Type de fichier :", "saved": "<PERSON><PERSON><PERSON>"}, "fileNew": "Nouveau fichier", "fileType": "Type de fichier :", "fileSaved": "<PERSON><PERSON><PERSON>", "fileUpload": "<PERSON><PERSON><PERSON>", "filesUpload": "Fichiers téléversés", "folderNew": "Nouveau dossier", "path": {"": "Répertoire", "missing": "Le répertoire n'existe pas.", "exists": "Le répertoire existe déjà.", "unableCreate": "Impossible de créer le répertoire.", "unableRename": "Impossible de renommer le répertoire."}, "restricted": {"": "Restrictions", "userList": "Vous n'êtes pas autorisé à éditer la liste des utilisateurs", "updates": "Vous n'êtes pas autorisé à vérifier si Atheos est à jour", "marketplace": "Vous n'êtes pas autorisé à accéder à la place de marché", "textmodes": "You can not access the Textmodes"}, "unauthorized": "Vous n'êtes pas autorisé à %s", "wrapLines": "Retours à la ligne", "printMarginShow": "Marge", "printMarginColumn": "<PERSON><PERSON> de colonne", "theme": "Thème", "hover": {"": "Survol", "duration": "Durée de survol des panneaux"}, "click": {"": "C<PERSON>r", "single": "Simple Click", "double": "Double Click"}, "loop": {"behavior": "Boucle sur les fichiers actifs", "onlyActive": "Boucle uniquement sur les onglets", "incDropdown": "Inclure les menus contextuels"}, "showHidden": "<PERSON><PERSON> les fichiers cachés", "trigger": {"fileManager": "Déclencheur du gestionnaire de fichier", "projectDock": "Déclencheur du dock projet", "rightSidebar": "<PERSON><PERSON><PERSON><PERSON><PERSON> du panneau droit", "leftSidebar": "Dé<PERSON>ncheur du panneau gauche"}, "contextMenu": {"delay": "<PERSON><PERSON><PERSON> de fermeture du menu contextuel"}, "newExtension": "Nouvelle extension", "fileExtension": "Extension de fichier", "defaultTextmode": "Mode texte par défaut", "indentGuides": "Guide d'indentation", "inlinePreview": "Prévisualisation en ligne", "close_uploader": "Fermer la fenêtre de téléchargement", "close_modal": "Fermer une fenêtre modale", "installationError": "Erreur d'installation", "existsAndWriteable": "Assurez-vous que les éléments suivants existent et sont autorisés pour l'écriture :", "envVariablesSet": "Assurez-vous que ces variables d'environnement sont définies :", "retest": "Tester à nouveau", "initialSetup": "Paramétrage initial", "developmentMode": "Mode développement", "connectionError": "Erreur de connexion", "market": {"": "Place de marché", "atheos": "Place de marché <PERSON>", "install": {"gitRepo": "...depuis un dépot Git", "manually": "Installer manuellement", "success": "%s installé avec succès"}, "unableDownload": "Impossible de télécharger l'archive.", "noZip": "L'extension zip n'est pas trouvée.", "missingDesc": "Il manque la description.", "missingAuth": "Inconnu"}, "complete": "Terminé !", "installed": "Installé", "available": "Disponible", "coming": "Bientôt disponible", "reloadAtheos": "Rechargement d'Atheos", "split": {"": "Partager", "h": "Partager horizontalement", "v": "Partager verticalement", "editor": {"h": "Partager l'éditeur horizontalement", "v": "Partager l'éditeur verticalement"}}, "merge": {"": "Assembler", "all": "Tout assembler", "editor": "Volets de l'éditeur de Merge"}, "time": {"ms": {"": "%s ms", "default": "%s ms (par défaut)"}, "second": {"single": "%s Seconde", "plural": "%s Secondes"}, "minute": {"single": "%s Minute", "plural": "%s Minutes"}, "hour": {"single": "%s Heure", "plural": "%s Heures"}}, "warning": {"fileOpen": "Attention : Le fichier est actuellement ouvert par %s"}, "NothingInYourClipboard": "Rien dans votre presse-papiers", "folderNameOrAbsolutePath": "Nom du dossier ou chemin absolu", "noOpenFilesOrSelect": "<PERSON><PERSON><PERSON> fi<PERSON>er ouvert ou texte sélectionné", "dragFilesOrClickHereToUpload": "Faites glisser des fichiers ou cliquez ici pour téléverser", "fileManager": "Gestionnaire de fichier", "gitRepository": "Dépôt git", "macro": {"": "Editeur <PERSON>", "add": "Ajouter une nouvelle macro", "title": "Titre", "icon": "FA Icône", "applies": "S'applique à", "exts": "Types de fichier", "cmd": "Commande shell"}, "codegit": {"": "CodeGit", "open": "Ouvrir CodeGit", "banner": "Bannière au-dessus du gestionnaire de fichier", "status": "Statut du fichier dans la barre d'état", "unknown": "Inconnu", "untracked": "Non suivi", "uncommitted": "Non committé", "committed": "<PERSON><PERSON><PERSON><PERSON>", "error": {"statusFail": "Impossible de charger le statut du dépôt"}, "diff": "<PERSON><PERSON><PERSON>", "blame": "<PERSON><PERSON><PERSON>", "log": "<PERSON><PERSON><PERSON> de <PERSON>"}, "git": {"error": {"noRepo": "Dépôt git non trouvé.", "noPath": "Répertoire manquant.", "noRepoFileMsg": "<PERSON><PERSON><PERSON><PERSON><PERSON>, fi<PERSON>er ou message manquant.", "noRepoFile": "<PERSON><PERSON><PERSON><PERSON><PERSON> ou fi<PERSON><PERSON> manqua<PERSON>.", "noPathUrl": "Répertoire ou URL manquant.", "noTypeRepoRemoteBranch": "Type, dé<PERSON><PERSON><PERSON>, site distant ou branche manquant.", "noRepoType": "<PERSON><PERSON><PERSON><PERSON><PERSON> ou type manquant.", "invalidAction": "Action invalide."}, "init": {"": "Git Init", "success": "Dépôt Git initialisé.", "failed": "L'initialisation du dépôt a échoué."}, "clone": {"": "<PERSON><PERSON>", "success": "Clonage du dépôt ok.", "failed": "Clonage du dépôt en erreur."}, "repoURL": {"": "URL du dépôt Github :", "invalid": "URL de dépôt invalide"}, "status": {"": "Statut Git :", "current": "La branche est à jour", "ahead": {"single": "Devant %s Commit", "plural": "Devant %s Commits"}, "behind": {"single": "Derrière %s Commit", "plural": "Derrière %s Commits"}}, "diff": "<PERSON><PERSON>", "blame": "<PERSON><PERSON>", "log": "Git Log", "undo": {"": "<PERSON><PERSON>", "file": "Annuler les changements sur %s ?", "success": "Les changements ont été annulés.", "failed": "Impossible d'annuler les changements."}, "pull": {"": "<PERSON><PERSON>", "success": "Pull ok.", "failed": "Pull en erreur."}, "push": {"": "<PERSON><PERSON>", "success": "Push ok.", "failed": "Push en erreur."}, "fetch": {"": "<PERSON>tch", "success": "Fetch ok.", "failed": "Fetch en erreur."}, "remote": {"new": "Nouveau site distant"}, "branch": {"new": "Nouvelle branche"}, "transfer": {"": "Transfert Git", "edit": "Editer les options de transfert"}, "addFailed": "L'ajout de %s a échoué", "commit": {"": "Commit", "message": "Message commit (facultatif pour un Amend)", "success": "<PERSON> commit a fonctionné.", "failed": "<PERSON> commit a échoué."}, "amend": {"": "Amend", "success": "Le commit (option amend) a fonctionné.", "failed": "Le commit (option amend) a échoué."}, "settings": {"local": "Paramètres Git pour %s", "global": "Paramètres Git globaux", "save": {"success": "Paramètres sauvegardés.", "failed": "Impossible de sauvegarder les paramètres."}, "apply": {"success": "Paramètres appliqués.", "failed": "Impossible d'appliquer les paramètres."}}, "objects": {"status": "Statut", "file": "<PERSON><PERSON><PERSON>", "actions": {"": "Actions", "diff": "<PERSON>ff<PERSON><PERSON><PERSON>", "undo": "<PERSON><PERSON><PERSON><PERSON>"}}}, "overview": "Prévisualisation", "log": "Trace", "transfer": "<PERSON><PERSON><PERSON><PERSON>", "configure": "Configurer"}