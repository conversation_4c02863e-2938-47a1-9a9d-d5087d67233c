#EDITOR {
	width: auto;
	height: 100%;
	transition: margin $transitionDuration ease-in-out;
	grid-area: editor;
}

.editor {
	width: 100%;
	height: 100%;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.ace_editor {
// 	position: absolute !important;
}

.ace_content {
	padding: 0;
	margin: 0;
}

.ace_gutter {
	z-index: 1;
}

.ace_gutter-layer {
	padding: 0;
	background-image: linear-gradient(135deg, $mideground 8.33%, transparent 8.33%, transparent 50%, $mideground 50%, $mideground 58.33%, transparent 58.33%, transparent 100%);
	background-size: 6.00px 6.00px;
	color: $fontColorMinor;
}

.ace_scrollbar {
	z-index: 1;
}

.ace_error {
	background-position: 3px 0 !important;
}

#ROOTEDITORWRAPPER {
	// Calculated using the height of the top & bottom bars
	// height: calc(100% - (23px + 33px));
	display: flex;
	height: 100%;
	position: relative;
}

#EDITOR,
.editorWindow {
	display: flex;
// 	width: 100%;
// 	height: 100%;
	
	&.horizontal {
	    	flex-direction: row;

	}
	&.vertical {
	    	flex-direction: column;

	}
}

.editorWindow,
.editorPane {
	flex: 1 1 auto;
	overflow: hidden;
	position: relative;
}

// .editor-wrapper-horizontal>.editor {
// 	top: 0;
// }

// .editor-wrapper-horizontal>.editor-wrapper {
// 	top: 0;
// }

// .editor-wrapper-vertical>.editor {
// 	left: 0;
// }

// .editor-wrapper-vertical>.editor-wrapper {
// 	left: 0;
// }

@import "bottom_bar", "splitter";