.ace_search {
	border-radius: 0 !important;
	border: 1px solid $shade2 !important;
	color: unset;
	background-color: $foreground;
	padding: 5px;
	padding-top: 28px;

	.ace_search_form,
	.ace_replace_form {
		margin: 0;
		margin-bottom: 5px;
		overflow: hidden;
		line-height: 1.9;
	}

	.ace_search_form.ace_nomatch {
		outline: none;
	}

	.ace_nomatch {
		.ace_search_field {
			background-color: $maroon;
		}
	}

	.ace_search_field {
		border-radius: 0 !important;
		font-size: inherit;
		margin: 0;
		line-height: inherit;
		padding: 3px;
		width: 15em;
		min-width: auto;
		vertical-align: middle;

		@include input();
	}

	.ace_searchbtn {
		border-radius: 0 !important;
		line-height: inherit;
		display: inline-block;
		padding: 3px;
		cursor: pointer;
		margin: 0;
		position: relative;
		width: 4.5em;
		text-align: center;
		font-family: 'Ubuntu', sans-serif;
		vertical-align: middle;

		@include input();

		border-left: $borderLight;

		&:hover {
			background: var(--hoverMajor);
		}

		&:last-of-type {
			width: 3em;
		}
	}

	.ace_searchbtn.prev,
	.ace_searchbtn.next {
		padding-bottom: 8px;
		padding-top: 9px;
		width: calc(4.5em / 2);

		&::after {
			display: none;
		}
	}

	.ace_searchbtn.prev::before {
		content: "\f053";
	}

	.ace_searchbtn.next::before {
		content: "\f054";
	}

	.ace_searchbtn.prev,
	.ace_searchbtn.next,
	.ace_searchbtn_close {
		font-family: 'Font Awesome 6 Free';
		font-weight: 900;
		-webkit-font-smoothing: antialiased;
		display: inline-block;
		font-style: normal;
		font-variant: normal;
		text-rendering: auto;
		line-height: 1;
	}

	.ace_searchbtn_close {
		background: none;
		color: $fontColorMajor;
		right: 9px;
		position: absolute;
		top: 6px;

		&::before {
			content: "\f057";
		}
	}

	.ace_searchbtn_close:hover {
		background-color: #656565;
		background-position: 50% 100%;
		color: white;
	}

	.ace_button {
		margin-left: 0;
		margin-top: 0 !important;
		box-sizing: border-box !important;
		display: inline-block;
		padding: 2px;
		width: 18px;
		height: 18px;
		text-align: center;

		@include input();

		&:hover {
			background: var(--hoverMajor);
		}
	}

	.ace_button+.ace_button {
		margin-left: 2px;
	}

	.ace_button:active {
		background-color: #ddd;
	}

	.ace_button.checked {
		border-color: $colorMajor;
		opacity: 1;
	}

	.ace_search_options {
		margin-bottom: 0;
		margin-right: 25px;
		width: calc(100% - 35px);
		position: absolute;
		top: 6px;

		.ace_button:first-of-type {
			font-family: 'Ubuntu-Fira', monospace;
			color: $fontColorMajor;
			line-height: 1.3;
		}
	}

	.ace_search_counter {
		float: left;
		margin-top: 2px;
		font-family: 'Ubuntu-Fira', monospace;
	}
}