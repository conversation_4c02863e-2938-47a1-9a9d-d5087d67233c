#Run on new commit
on:
  push:
    branches:
    - main
#Naming
name: Build Docker Image
run-name: Building Docker Image from commit ${{ github.sha }}
jobs:
  build-image:
    runs-on: ubuntu-latest
    steps:
      #Setup docker stuff (qemu and buildx)
      - name: Setup QEMU
        uses: docker/setup-qemu-action@v2
      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v2
      #Login to docker hub for building (make sure secrets are set)
      - name: Login to Docker Hub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}
      #Download Dockerfile
      - name: Get Dockerfile
        uses: actions/checkout@v2.7.0
        with:
          repository: Atheos/Atheos-Docker
      # Build and upload
      - name: Build and Upload
        uses: docker/build-push-action@v4
        with:
          context: .
          push: true
          tags: hlsiira/atheos:latest
