<?php

//////////////////////////////////////////////////////////////////////////////80
// Context Menu Controller
//////////////////////////////////////////////////////////////////////////////80
// Copyright (c) Athe<PERSON> & <PERSON> (Atheos.io), distributed as-is and without
// warranty under the MIT License. See [root]/docs/LICENSE.md for more.
// This information must remain intact.
//////////////////////////////////////////////////////////////////////////////80
// Authors: <AUTHORS>
//////////////////////////////////////////////////////////////////////////////80

require_once "class.contextmenu.php";

$ContextMenu = new ContextMenu();

switch ($action) {
	//////////////////////////////////////////////////////////////////
	// Read Context Menu JSONs
	//////////////////////////////////////////////////////////////////
	case 'loadMenus':
		$ContextMenu->loadMenus();
		break;

	default:
		Common::send(416, "Invalid action.");
		break;
}