// Turn off antialiasing for macOS
@mixin thicken{
	-webkit-font-smoothing: subpixel-antialiased;
}

// Accentuate an icon's edges
@mixin sharpen{
	text-shadow: 0 0 0;
}



/*============================================================================*
  Octicons
  https://github.com/github/octicons
/*============================================================================*/
@mixin octicons { font-family: "Octicons Regular"; font-size: 18px; top: 1px; }

.binary-icon:before       { @include octicons; content: "\f094"; }
.book-icon:before         { @include octicons; content: "\f007"; }
.brew-icon:before         { @include octicons; content: "\f069"; font-size: 15px; left: 1px; }
.checklist-icon:before    { @include octicons; content: "\f076"; font-size: 17px; left: 1px; }
.code-icon:before         { @include octicons; content: "\f05f"; }
.database-icon:before     { @include octicons; content: "\f096"; }
.gear-icon:before         { @include octicons; content: "\f02f"; }
.git-commit-icon:before   { @include octicons; content: "\f01f"; }
.git-merge-icon:before    { @include octicons; content: "\f023"; }
.github-icon:before       { @include octicons; content: "\f00a"; }
.graph-icon:before        { @include octicons; content: "\f043"; }
.image-icon:before        { @include octicons; content: "\f012"; }
.key-icon:before          { @include octicons; content: "\f049"; }
.link-icon:before         { @include octicons; content: "\f0b0"; }
.markdown-icon:before     { @include octicons; content: "\f0c9"; }
.package-icon:before      { @include octicons; content: "\f0c4"; }
.ruby-icon:before         { @include octicons; content: "\f047"; }
.secret-icon:before       { @include octicons; content: "\f08c"; }
.squirrel-icon:before     { @include octicons; content: "\f0b2"; font-size: 15px; }
.text-icon:before         { @include octicons; content: "\f011"; }
.zip-icon:before          { @include octicons; content: "\f013"; }




/*============================================================================*
  FontAwesome
  http://fortawesome.github.io/Font-Awesome/cheatsheet
/*============================================================================*/
@mixin fas-icon { font-family: 'Font Awesome 6 Free'; font-weight: 900; font-size: 13px; }
@mixin fab-icon { font-family: 'Font Awesome 6 Brands'; font-weight: 400; font-size: 13px; }

.anchor-icon:before       { @include fas-icon; content: "\f13d"; font-size: 16px; top: 1px; }
.android-icon:before      { @include fab-icon; content: "\f17b"; font-size: 16px; top: 1px; }
.at-icon:before           { @include fas-icon; content: "\f1fa"; font-size: 15px; top: 1px; }
.audio-icon:before        { @include fas-icon; content: "\f028"; font-size: 15px; top: 1px; }
.backup-icon:before       { @include fas-icon; content: "\f1da"; font-size: 13px; top: 1px; }
.book-alt-icon:before     { @include fas-icon; content: "\f02d"; font-size: 16px; top: 2px; }
.bullhorn-icon:before     { @include fas-icon; content: "\f0a1"; font-size: 16px; top: 2px; }
.calc-icon:before         { @include fas-icon; content: "\f1ec"; font-size: 14px; }
.coffee-icon:before       { @include fas-icon; content: "\f0f4"; font-size: 14px; top: 1px; }
.css3-icon:before         { @include fab-icon; content: "\f13c"; top: 0; }
.circle-icon:before       { @include fas-icon; content: "\f111"; font-size: 16px; top: 1px; }
.download-icon:before     { @include fas-icon; content: "\f019"; font-size: 16px; top: 2px; }
.earth-icon:before        { @include fas-icon; content: "\f57d"; font-size: 15px; }
.gears-icon:before        { @include fas-icon; content: "\f085"; font-size: 15px; }
.html5-icon:before        { @include fab-icon; content: "\f13b"; font-size: 15px; top: 1px; }
.lock-icon:before         { @include fas-icon; content: "\f023"; font-size: 17px; top: 2px; }
.mobile-icon:before       { @include fas-icon; content: "\f10b"; font-size: 20px; top: 2px; }
.moon-icon:before         { @include fas-icon; content: "\f186"; font-size: 16px; top: 1px; } //Maybe
.music-icon:before        { @include fas-icon; content: "\f001"; font-size: 15px; }
.print-icon:before        { @include fas-icon; content: "\f02f"; font-size: 15px; top: 2px; }
.recycle-icon:before      { @include fas-icon; content: "\f1b8"; font-size: 15px; top: 2px; }
.rss-icon:before          { @include fas-icon; content: "\f143"; font-size: 16px; top: 2px; }
.smarty-icon:before       { @include fas-icon; content: "\f0eb"; font-size: 15px; } //Who knows?
.sourcemap-icon:before    { @include fas-icon; content: "\f279"; font-size: 14px; }
.sun-icon:before          { @include fas-icon; content: "\f185"; font-size: 14px; @include thicken; }
.toc-icon:before          { @include fas-icon; content: "\f03a"; font-size: 15px; top: 2px; } // Maybe
.wechat-icon:before       { @include fab-icon; content: "\f1d7"; font-size: 16px; top: 2px; }
.pdf-icon:before          { @include fas-icon; content: "\f1c1"; top: 2px; }



/*============================================================================*
  Mfizz
  http://mfizz.com/oss/font-mfizz
/*============================================================================*/
@mixin mfizz { font-family: Mfizz; font-size: 14px; }

.apache-icon:before       { @include mfizz; content: "\f102"; top: 3px; font-size: 15px; }
.archlinux-icon:before    { @include mfizz; content: "A";     top: 1px; font-size: 15px; }
.c-icon:before            { @include mfizz; content: "\f106"; top: 1px; font-size: 13px; }
.cpp-icon:before          { @include mfizz; content: "\f10b"; top: 1px; }
.csharp-icon:before       { @include mfizz; content: "\f10c"; top: 1px; }
.debian-icon:before       { @include mfizz; content: "\f111"; top: 1px; }
.elixir-icon:before       { @include mfizz; content: "\f113"; top: 1px; }
.gnome-icon:before        { @include mfizz; content: "\f119"; top: 1px; }
.haskell-icon:before      { @include mfizz; content: "\f121"; top: 2px; font-size: 16px; }
.java-icon:before         { @include mfizz; content: "\f126"; top: 2px; font-size: 16px; }
.js-icon:before           { @include mfizz; content: "\f129"; top: 1px; font-size: 14px; }
.msql-icon:before         { @include mfizz; content: "\f136"; top: 2px; font-size: 15px; @include sharpen; }
.objc-icon:before         { @include mfizz; content: "\f13e"; top: 2px; font-size: 16px; }
.osx-icon:before          { @include mfizz; content: "\f141"; top: 1px; }
.pgsql-icon:before        { @include mfizz; content: "\f14a"; top: 2px; font-size: 16px; }
.python-icon:before       { @include mfizz; content: "\f14c"; top: 1px; }
.red-hat-icon:before      { @include mfizz; content: "\f14e"; top: 2px; }
.scala-icon:before        { @include mfizz; content: "\f154"; top: 1px; }
.sql-icon:before          { @include mfizz; content: "\f10e"; top: 1px; }
.svg-icon:before          { @include mfizz; content: "\f15c"; top: 1px; }
.x11-icon:before          { @include mfizz; content: "\f16e"; top: 1px; font-size: 13px; }



/*============================================================================*
  Devicons
  http://vorillaz.github.io/devicons
/*============================================================================*/
@mixin devicons { font-family: Devicons; font-size: 16px; top: 3px; }

.angular-icon:before      { @include devicons; content: "\e653"; }
.appcelerator-icon:before { @include devicons; content: "\e6ab"; }
.appstore-icon:before     { @include devicons; content: "\e613"; }
.asp-icon:before          { @include devicons; content: "\e67f"; }
.atom-icon:before         { @include devicons; content: "\e664"; @include thicken; }
.backbone-icon:before     { @include devicons; content: "\e652"; }
.bitbucket-icon:before    { @include devicons; content: "\e603"; }
.bootstrap-icon:before    { @include devicons; content: "\e647"; font-size: 15px; top: 2px; }
.bower-icon:before        { @include devicons; content: "\e64d"; @include sharpen; }
.chrome-icon:before       { @include devicons; content: "\e643"; }
.clojure-icon:before      { @include devicons; content: "\e668"; @include thicken; }
.compass-icon:before      { @include devicons; content: "\e661"; font-size: 14px; top: 2px; }
.dart-icon:before         { @include devicons; content: "\e698"; font-size: 15px; top: 2px; }
.dlang-icon:before        { @include devicons; content: "\e6af"; }
.dojo-icon:before         { @include devicons; content: "\e61c"; font-size: 16px; top: 4px; transform: scale(1.2); @include thicken; }
.dropbox-icon:before      { @include devicons; content: "\e607"; }
.eclipse-icon:before      { @include devicons; content: "\e69e"; }
.erlang-icon:before       { @include devicons; content: "\e6b1"; }
.extjs-icon:before        { @include devicons; content: "\e68e"; }
.firefox-icon:before      { @include devicons; content: "\e645"; }
.fsharp-icon:before       { @include devicons; content: "\e6a7"; left: 1px; top: 2px; }
.git-icon:before          { @include devicons; content: "\e602"; font-size: 15px; top: 2px; }
.heroku-icon:before       { @include devicons; content: "\e67b"; }
.jekyll-icon:before       { @include devicons; content: "\e60d"; font-size: 16px; @include sharpen; }
.jquery-icon:before       { @include devicons; content: "\e650"; font-size: 15px; top: 2px; }
.jqueryui-icon:before     { @include devicons; content: "\e654"; font-size: 15px; top: 2px; }
.laravel-icon:before      { @include devicons; content: "\e63f"; @include thicken; }
.materialize-icon:before  { @include devicons; content: "\e6b6"; transform: scale(1.2); @include thicken; }
.modernizr-icon:before    { @include devicons; content: "\e620"; }
.mootools-icon:before     { @include devicons; content: "\e68f"; @include sharpen; }
.node-icon:before         { @include devicons; content: "\e618"; }
.perl-icon:before         { @include devicons; content: "\e669"; font-size: 15px; top: 2px; }
.prolog-icon:before       { @include devicons; content: "\e6a1"; }
.rails-icon:before        { @include devicons; content: "\e63b"; }
.raphael-icon:before      { @include devicons; content: "\e65f"; font-size: 15px; }
.requirejs-icon:before    { @include devicons; content: "\e670"; }
.rust-icon:before         { @include devicons; content: "\e6a8"; }
.safari-icon:before       { @include devicons; content: "\e648"; }
.sass-icon:before         { @include devicons; content: "\e64b"; }
.sencha-icon:before       { @include devicons; content: "\e68c"; }
.snapsvg-icon:before      { @include devicons; content: "\e65e"; }
.swift-icon:before        { @include devicons; content: "\e655"; left: -1px; }
.travis-icon:before       { @include devicons; content: "\e67e"; font-size: 15px; top: 2px; }
.typo3-icon:before        { @include devicons; content: "\e672"; }
.uikit-icon:before        { @include devicons; content: "\e673"; font-size: 15px; top: 2px; }
.unity3d-icon:before      { @include devicons; content: "\e621"; }
.vim-icon:before          { @include devicons; content: "\e6c5"; }
.vs-icon:before           { @include devicons; content: "\e60c"; font-size: 14px; top: 2px; }
.windows-icon:before      { @include devicons; content: "\e60f"; font-size: 14px; top: 2px; }
.yeoman-icon:before       { @include devicons; content: "\e67a"; }




/*============================================================================*
  Custom file icons
  See https://github.com/file-icons/source/charmap.md
/*============================================================================*/
@mixin file-icon { font-family: file-icons; font-size: 15px; }
@mixin test-file { top: 3px; font-size: 17px; }

._1c-icon:before           { @include file-icon; content: "\a5ea"; top: 3px; font-size: 16px; }
._1c-alt-icon:before       { @include file-icon; content: "\ea28"; top: 3px; font-size: 16px; }
.abap-icon:before          { @include file-icon; content: "\e92b"; top: 2px; }
.abif-icon:before          { @include file-icon; content: "\ea4e"; top: 3px; font-size: 16px; }
.access-icon:before        { @include file-icon; content: "\e9ea"; top: 2px; }
.acre-icon:before          { @include file-icon; content: "\237a"; top: 3px; font-size: 16px; }
.ada-icon:before           { @include file-icon; content: "\e90b"; top: 3px; font-size: 17px; }
.ae-icon:before            { @include file-icon; content: "\e9f3"; top: 2px; }
.affectscript-icon:before  { @include file-icon; content: "\eb2c"; top: 3px; font-size: 16px; }
.affinity-icon:before      { @include file-icon; content: "\eabb"; top: 2px; }
.ahk-icon:before           { @include file-icon; content: "\e932"; top: 2px; }
.ai-icon:before            { @include file-icon; content: "\e6b4"; top: 2px; }
.alacritty-icon:before     { @include file-icon; content: "\eb27"; top: 3px; font-size: 16px; }
.alacritty-alt-icon:before { @include file-icon; content: "\eb28"; top: 2px; font-size: 16px; }
.alex-icon:before          { @include file-icon; content: "\29cb"; top: 4px; font-size: 16px; @include thicken; }
.alloy-icon:before         { @include file-icon; content: "\e935"; top: 2px; }
.alpine-icon:before        { @include file-icon; content: "\e9ff"; top: 2px; font-size: 16px; }
.ampl-icon:before          { @include file-icon; content: "\e94e"; top: 3px; font-size: 16px; left: 1px; }
.amusewiki-icon:before     { @include file-icon; content: "\eb32"; top: 3px; font-size: 17px; }
.amx-icon:before           { @include file-icon; content: "\e99b"; top: 3px; font-size: 16px; }
.angelscript-icon:before   { @include file-icon; content: "\ea5b"; top: 4px; font-size: 18px; left: -2px; transform: scale(1.3); @include sharpen; }
.animestudio-icon:before   { @include file-icon; content: "\eaed"; top: 2px; }
.ansible-icon:before       { @include file-icon; content: "\24b6"; top: 2px; }
.ant-icon:before           { @include file-icon; content: "\e93e"; top: 4px; font-size: 18px; transform: scale(1.1); }
.antlr-icon:before         { @include file-icon; content: "\e92c"; top: 3px; }
.antwar-icon:before        { @include file-icon; content: "\2591"; top: 3px; font-size: 16px; }
.anyscript-icon:before     { @include file-icon; content: "\eacf"; top: 4px; font-size: 17px; }
.api-icon:before           { @include file-icon; content: "\e92d"; top: 2px; }
.apl-icon:before           { @include file-icon; content: "\234b"; top: 2px; }
.apple-icon:before         { @include file-icon; content: "\e925"; top: 1px; }
.appveyor-icon:before      { @include file-icon; content: "\e923"; top: 2px; }
.arc-icon:before           { @include file-icon; content: "\e92f"; top: 2px; }
.arduino-icon:before       { @include file-icon; content: "\e930"; top: 3px; font-size: 16px; }
.arttext-icon:before       { @include file-icon; content: "\24d0"; top: 2px; }
.as-icon:before            { @include file-icon; content: "\e92e"; top: 1px; font-size: 14px; }
.asciidoc-icon:before      { @include file-icon; content: "\e918"; top: 1px; font-size: 14px; }
.asciidoctor-icon:before   { @include file-icon; content: "\eac7"; top: 2px; font-size: 16px; @include sharpen; }
.asymptote-icon:before     { @include file-icon; content: "\eae5"; top: 3px; font-size: 16px; @include thicken; }
.atoum-icon:before         { @include file-icon; content: "\2649"; top: 2px; font-size: 16px; }
.ats-icon:before           { @include file-icon; content: "\e934"; top: 2px; }
.audacity-icon:before      { @include file-icon; content: "\e9f9"; top: 2px; }
.augeas-icon:before        { @include file-icon; content: "\e931"; top: 2px; }
.aurelia-icon:before       { @include file-icon; content: "\ea48"; top: 2px; }
.autoit-icon:before        { @include file-icon; content: "\e933"; top: 2px; font-size: 16px; }
.azurepipelines-icon:before{ @include file-icon; content: "\1f680";top: 2px; }
.avro-icon:before          { @include file-icon; content: "\eaa3"; top: 4px; font-size: 16px; left: 1px; transform: scale(1.2); }
.babel-icon:before         { @include file-icon; content: "\e91f"; top: 2px; left: 1px; }
.bazaar-icon:before        { @include file-icon; content: "\eacd"; top: 2px; font-size: 16px; @include sharpen; }
.bazel-icon:before         { @include file-icon; content: "\ead2"; top: 2px; font-size: 16px; @include sharpen; }
.behat-icon:before         { @include file-icon; content: "\ea89"; top: 2px; }
.bem-icon:before           { @include file-icon; content: "\ea59"; top: 2px; }
.bibtex-icon:before        { @include file-icon; content: "\e601"; top: 2px; font-size: 16px; @include thicken; }
.biml-icon:before          { @include file-icon; content: "\eb29"; top: 2px; }
.bintray-icon:before       { @include file-icon; content: "\ea6e"; top: 2px; }
.bithound-icon:before      { @include file-icon; content: "\ea2a"; top: 2px; }
.blender-icon:before       { @include file-icon; content: "\e9fa"; top: 2px; }
.bluespec-icon:before      { @include file-icon; content: "\e93c"; top: 1px; font-size: 13px; left: 1px; }
.bnf-icon:before           { @include file-icon; content: "\2a74"; top: 4px; font-size: 16px; }
.boo-icon:before           { @include file-icon; content: "\e939"; top: 2px; }
.boot-icon:before          { @include file-icon; content: "\f103"; top: 2px; font-size: 16px; }
.brain-icon:before         { @include file-icon; content: "\e93a"; top: 2px; }
.brakeman-icon:before      { @include file-icon; content: "\e9d6"; top: 2px; }
.bro-icon:before           { @include file-icon; content: "\e93b"; top: 3px; font-size: 16px; }
.broccoli-icon:before      { @include file-icon; content: "\e922"; top: 1px; font-size: 14px; }
.brotli-icon:before        { @include file-icon; content: "\eaa2"; top: 2px; }
.browserslist-icon:before  { @include file-icon; content: "\ea80"; top: 2px; }
.browsersync-icon:before   { @include file-icon; content: "b";     top: 2px; }
.brunch-icon:before        { @include file-icon; content: "\ea47"; top: 3px; font-size: 17px; left: -1px; }
.buck-icon:before          { @include file-icon; content: "\ea46"; top: 2px; }
.bundler-icon:before       { @include file-icon; content: "\ea45"; top: 2px; font-size: 16px; }
.byond-icon:before         { @include file-icon; content: "\e962"; top: 2px; }
.cabal-icon:before         { @include file-icon; content: "\e9c2"; top: 2px; }
.caddy-icon:before         { @include file-icon; content: "\1f512";top: 2px; }
.caffe-icon:before         { @include file-icon; content: "\ea9a"; top: 3px; font-size: 17px; }
.caffe2-icon:before        { @include file-icon; content: "\eab6"; top: 2px; }
.cake-icon:before          { @include file-icon; content: "\e9e3"; top: 2px; }
.cakefile-icon:before      { @include file-icon; content: "\e924"; top: 2px; }
.cakephp-icon:before       { @include file-icon; content: "\ea43"; top: 2px; }
.carthage-icon:before      { @include file-icon; content: "\ea98"; top: 2px; }
.cc-icon:before            { @include file-icon; content: "\e9d5"; top: 2px; font-size: 16px; }
.ceylon-icon:before        { @include file-icon; content: "\e94f"; top: 2px; }
.cf-icon:before            { @include file-icon; content: "\e929"; top: 2px; }
.chai-icon:before          { @include file-icon; content: "c";     top: 3px; font-size: 16px; }
.chapel-icon:before        { @include file-icon; content: "\e950"; top: 2px; }
.chartjs-icon:before       { @include file-icon; content: "\ea0b"; top: 2px; }
.chef-icon:before          { @include file-icon; content: "\ea42"; top: 2px; }
.chocolatey-icon:before    { @include file-icon; content: "\1f36b";top: 3px; font-size: 17px; }
.chuck-icon:before         { @include file-icon; content: "\e943"; top: 2px; }
.circleci-icon:before      { @include file-icon; content: "\ea12"; top: 2px; font-size: 14px; }
.cirru-icon:before         { @include file-icon; content: "\e951"; top: 2px; @include sharpen; }
.cl-icon:before            { @include file-icon; content: "\e972"; top: 2px; @include sharpen; }
.clarion-icon:before       { @include file-icon; content: "\e952"; top: 1px; font-size: 14px; left: 1px; }
.clean-icon:before         { @include file-icon; content: "\e95b"; top: 2px; font-size: 16px; }
.click-icon:before         { @include file-icon; content: "\e95c"; top: 2px; }
.clips-icon:before         { @include file-icon; content: "\e940"; top: 3px; font-size: 18px; }
.cljs-icon:before          { @include file-icon; content: "\f104"; top: 2px; }
.closure-tpl-icon:before   { @include file-icon; content: "\ea82"; top: 2px; }
.cloudfoundry-icon:before  { @include file-icon; content: "\ead0"; top: 2px; }
.cmake-icon:before         { @include file-icon; content: "\e93f"; top: 1px; font-size: 14px; }
.cobol-icon:before         { @include file-icon; content: "\ea44"; top: 2px; font-size: 16px; }
.cocoapods-icon:before     { @include file-icon; content: "\ea97"; top: 2px; }
.codacy-icon:before        { @include file-icon; content: "\ea8b"; top: 2px; }
.codecov-icon:before       { @include file-icon; content: "\2602"; top: 2px; }
.codekit-icon:before       { @include file-icon; content: "\ea41"; top: 2px; }
.codeship-icon:before      { @include file-icon; content: "\ea6a"; top: 2px; }
.composer-icon:before      { @include file-icon; content: "\e683"; top: 3px; font-size: 17px; }
.conan-icon:before         { @include file-icon; content: "\ead1"; top: 2px; }
.conda-icon:before         { @include file-icon; content: "\eb3c"; top: 2px; font-size: 16px; }
.config-icon:before        { @include file-icon; content: "\f07c"; top: 2px; font-size: 14px; }
.config-coffee-icon:before { @include file-icon; content: "\eb18"; top: 3px; font-size: 17px; }
.config-go-icon:before     { @include file-icon; content: "\eb12"; top: 3px; font-size: 18px; }
.config-hs-icon:before     { @include file-icon; content: "\eb14"; top: 3px; font-size: 17px; }
.config-js-icon:before     { @include file-icon; content: "\eb1a"; top: 3px; font-size: 17px; }
.config-perl-icon:before   { @include file-icon; content: "\eb19"; top: 3px; font-size: 17px; }
.config-python-icon:before { @include file-icon; content: "\eb15"; top: 3px; font-size: 17px; }
.config-react-icon:before  { @include file-icon; content: "\eb16"; top: 3px; font-size: 17px; }
.config-ruby-icon:before   { @include file-icon; content: "\eb17"; top: 3px; font-size: 17px; }
.config-rust-icon:before   { @include file-icon; content: "\eb13"; top: 3px; font-size: 17px; }
.config-ts-icon:before     { @include file-icon; content: "\eb1b"; top: 3px; font-size: 17px; }
.conll-icon:before         { @include file-icon; content: "\eaa6"; top: 2px; }
.coq-icon:before           { @include file-icon; content: "\e95f"; top: 2px; font-size: 16px; left: 1px; }
.cordova-icon:before       { @include file-icon; content: "\ea11"; top: 2px; }
.corel-icon:before         { @include file-icon; content: "\ea91"; top: 3px; font-size: 16px; }
.coreldraw-icon:before     { @include file-icon; content: "\ea90"; top: 2px; }
.coveralls-icon:before     { @include file-icon; content: "\272a"; top: 2px; }
.cp-icon:before            { @include file-icon; content: "\e942"; top: 3px; font-size: 17px; }
.cpan-icon:before          { @include file-icon; content: "\ea87"; top: 2px; }
.creole-icon:before        { @include file-icon; content: "\e95e"; top: 2px; }
.crowdin-icon:before       { @include file-icon; content: "\ead3"; top: 2px; font-size: 16px; @include sharpen; }
.crystal-icon:before       { @include file-icon; content: "\e902"; top: 2px; left: 1px; }
.csound-icon:before        { @include file-icon; content: "\e9f0"; top: 2px; }
.csscript-icon:before      { @include file-icon; content: "\e9e2"; top: 2px; }
.cucumber-icon:before      { @include file-icon; content: "\f02b"; top: 3px; }
.curl-icon:before          { @include file-icon; content: "/";     top: 3px; font-size: 16px; }
.cvs-icon:before           { @include file-icon; content: "\1f41f";top: 1px; font-size: 16px; }
.cwl-icon:before           { @include file-icon; content: "\29d9"; top: 2px; font-size: 16px; }
.cython-icon:before        { @include file-icon; content: "\e963"; top: 2px; }
.d3-icon:before            { @include file-icon; content: "\ea10"; top: 2px; }
.darcs-icon:before         { @include file-icon; content: "\e964"; top: 2px; }
.dashboard-icon:before     { @include file-icon; content: "\f07d"; top: 2px; font-size: 13px; }
.dataweave-icon:before     { @include file-icon; content: "\ea99"; top: 2px; }
.dbase-icon:before         { @include file-icon; content: "\e9f1"; top: 2px; }
.dna-icon:before           { @include file-icon; content: "\2624"; top: 2px; font-size: 16px; }
.default-icon:before       { @include file-icon; content: "\1f5cc";top: 2px; font-size: 14px; }
.delphi-icon:before        { @include file-icon; content: "\ea40"; top: 2px; font-size: 16px; }
.deno-icon:before          { @include file-icon; content: "\eaef"; top: 2px; font-size: 16px; }
.dependabot-icon:before    { @include file-icon; content: "\eb3e"; top: 3px; font-size: 17px; }
.devicetree-icon:before    { @include file-icon; content: "\ea57"; top: 2px; font-size: 17px; @include sharpen; }
.dia-icon:before           { @include file-icon; content: "\eab5"; top: 2px; }
.digdag-icon:before        { @include file-icon; content: "\eb03"; top: 2px; }
.diff-icon:before          { @include file-icon; content: "\e960"; top: 2px; }
.docker-icon:before        { @include file-icon; content: "\f106"; top: 3px; font-size: 18px; }
.doclets-icon:before       { @include file-icon; content: "\ea3f"; top: 2px; }
.docpad-icon:before        { @include file-icon; content: "\21b9"; top: 4px; font-size: 17px; }
.doge-icon:before          { @include file-icon; content: "\e946"; top: 2px; }
.dosbox-icon:before        { @include file-icon; content: "\eaf2"; top: 2px; font-size: 16px; }
.dotjs-icon:before         { @include file-icon; content: "\eb0b"; top: 2px; font-size: 16px; }
.doxygen-icon:before       { @include file-icon; content: "\e928"; top: 1px; font-size: 13px; }
.dragula-icon:before       { @include file-icon; content: "\1f44c";top: 3px; font-size: 17px; }
.drone-icon:before         { @include file-icon; content: "\ea3d"; top: 2px; }
.dyalog-icon:before        { @include file-icon; content: "\e90c"; top: 1px; font-size: 14px; left: 1px; }
.dylib-icon:before         { @include file-icon; content: "\ea15"; top: 2px; }
.e-icon:before             { @include file-icon; content: "E";     top: 1px; font-size: 14px; }
.eagle-icon:before         { @include file-icon; content: "\e965"; top: 2px; }
.easybuild-icon:before     { @include file-icon; content: "\ea85"; top: 2px; }
.ec-icon:before            { @include file-icon; content: "\e9c9"; top: 2px; }
.ecere-icon:before         { @include file-icon; content: "\e966"; top: 3px; font-size: 16px; }
.editorconfig-icon:before  { @include file-icon; content: "\ea1b"; top: 3px; }
.edge-icon:before          { @include file-icon; content: "\ea78"; top: 2px; }
.eiffel-icon:before        { @include file-icon; content: "\e967"; top: 2px; font-size: 16px; }
.ejs-icon:before           { @include file-icon; content: "\ea4b"; top: 3px; font-size: 16px; }
.electron-icon:before      { @include file-icon; content: "\ea27"; top: 3px; font-size: 16px; @include sharpen; }
.elm-icon:before           { @include file-icon; content: "\f102"; top: 2px; }
.em-icon:before            { @include file-icon; content: "\e968"; top: 3px; font-size: 16px; }
.emacs-icon:before         { @include file-icon; content: "\e926"; top: 2px; }
.ember-icon:before         { @include file-icon; content: "\e61b"; top: 2px; font-size: 14px; }
.ensime-icon:before        { @include file-icon; content: "\ead4"; top: 2px; }
.eq-icon:before            { @include file-icon; content: "\ea0a"; top: 5px; }
.esdoc-icon:before         { @include file-icon; content: "\ea5c"; top: 2px; }
.eslint-icon:before        { @include file-icon; content: "\ea0f"; top: 3px; font-size: 16px; }
.excel-icon:before         { @include file-icon; content: "\e9ee"; top: 2px; }
.fabfile-icon:before       { @include file-icon; content: "\e94b"; top: 2px; font-size: 16px; }
.factor-icon:before        { @include file-icon; content: "\e96a"; top: 3px; font-size: 18px; left: -2px; transform: scale(1.2); }
.falcon-icon:before        { @include file-icon; content: "\eae4"; top: 2px; }
.fancy-icon:before         { @include file-icon; content: "\e96b"; top: 2px; font-size: 16px; }
.fantom-icon:before        { @include file-icon; content: "\e96f"; top: 2px; left: 1px; }
.faust-icon:before         { @include file-icon; content: "\22c0"; top: 2px; }
.fbx-icon:before           { @include file-icon; content: "\e9fc"; top: 2px; }
.fexl-icon:before          { @include file-icon; content: "\eb20"; top: 4px; font-size: 17px; }
.ff-icon:before            { @include file-icon; content: "\fb00"; top: 3px; }
.finder-icon:before        { @include file-icon; content: "\e9e9"; top: 3px; font-size: 16px; }
.firebase-icon:before      { @include file-icon; content: "\ea7f"; top: 2px; }
.firebase-bolt-icon:before { @include file-icon; content: "\26A1"; top: 3px; }
.flask-icon:before         { @include file-icon; content: "\1f704";top: 2px; }
.floobits-icon:before      { @include file-icon; content: "\ead5"; top: 3px; font-size: 16px; }
.flow-icon:before          { @include file-icon; content: "\e921"; top: 1px; }
.flutter-icon:before       { @include file-icon; content: "\eaeb"; top: 2px; left: -1px; }
.flux-icon:before          { @include file-icon; content: "\e969"; top: 2px; }
.font-icon:before          { @include file-icon; content: "\eaaa"; top: 2px; font-size: 16px; @include sharpen; }
.font-bitmap-icon:before   { @include file-icon; content: "\eaab"; top: 2px; }
.fortran-icon:before       { @include file-icon; content: "\e90a"; top: 1px; font-size: 14px; left: 1px; }
.fossa-icon:before         { @include file-icon; content: "\eac3"; top: 3px; font-size: 17px; }
.fossil-icon:before        { @include file-icon; content: "\ead6"; top: 2px; font-size: 17px; @include sharpen; }
.fountain-icon:before      { @include file-icon; content: "\1f135";top: 2px; font-size: 16px; }
.franca-icon:before        { @include file-icon; content: "\ea56"; top: 2px; }
.freemarker-icon:before    { @include file-icon; content: "\e970"; top: 2px; font-size: 16px; left: 1px; }
.frege-icon:before         { @include file-icon; content: "\e96e"; top: 2px; font-size: 16px; left: 1px; }
.fuelux-icon:before        { @include file-icon; content: "\ea09"; top: 3px; font-size: 16px; left: 2px; transform: scale(1.15); @include sharpen; }
.fusebox-icon:before       { @include file-icon; content: "\ead7"; top: 2px; }
.gams-icon:before          { @include file-icon; content: "\e973"; top: 2px; left: 1px; }
.galen-icon:before         { @include file-icon; content: "\ead8"; top: 2px; }
.gap-icon:before           { @include file-icon; content: "\e971"; top: 3px; font-size: 16px; left: 1px; }
.gatsby-icon:before        { @include file-icon; content: "\24bc"; top: 2px; }
.gdb-icon:before           { @include file-icon; content: "\ea08"; top: 3px; font-size: 16px; transform: scale(1.15); @include sharpen; }
.genshi-icon:before        { @include file-icon; content: "\e976"; top: 3px; }
.gentoo-icon:before        { @include file-icon; content: "\e96d"; top: 1px; font-size: 14px; left: 1px; }
.gf-icon:before            { @include file-icon; content: "\e978"; top: 2px; }
.gimp-icon:before          { @include file-icon; content: "\ea88"; top: 2px; font-size: 17px; transform: scale(1.15); }
.gitlab-icon:before        { @include file-icon; content: "\ea3c"; top: 3px; font-size: 16px; }
.glade-icon:before         { @include file-icon; content: "\e938"; top: 2px; }
.glide-icon:before         { @include file-icon; content: "\eacb"; top: 3px; font-size: 17px; transform: scale(1.15); }
.gltf-icon:before          { @include file-icon; content: "\eaa7"; top: 3px; font-size: 17px; }
.glyphs-icon:before        { @include file-icon; content: "G";     top: 3px; }
.gml-icon:before           { @include file-icon; content: "\e975"; top: 3px; font-size: 16px; }
.gn-icon:before            { @include file-icon; content: "\ea25"; top: 2px; }
.gnu-icon:before           { @include file-icon; content: "\e679"; top: 2px; font-size: 16px; @include sharpen; }
.gnuplot-icon:before       { @include file-icon; content: "\1f4c8";top: 3px; font-size: 16px; @include sharpen; }
.go-icon:before            { @include file-icon; content: "\eaae"; top: 4px; font-size: 18px; left: -1px; }
.godot-icon:before         { @include file-icon; content: "\e974"; top: 2px; }
.golo-icon:before          { @include file-icon; content: "\e979"; top: 2px; }
.gosu-icon:before          { @include file-icon; content: "\e97a"; top: 2px; }
.gradle-icon:before        { @include file-icon; content: "\e903"; top: 3px; font-size: 16px; left: 1px; }
.graphite-icon:before      { @include file-icon; content: "\ea8a"; top: 2px; font-size: 17px; }
.graphql-icon:before       { @include file-icon; content: "\e97c"; top: 2px; }
.graphviz-icon:before      { @include file-icon; content: "\e97d"; top: 4px; font-size: 17px; left: 1px; }
.greenkeeper-icon:before   { @include file-icon; content: "\eb0c"; top: 3px; font-size: 16px; }
.gvdesign-icon:before      { @include file-icon; content: "\eb02"; top: 2px; }
.gridsome-icon:before      { @include file-icon; content: "\eae7"; top: 2px; }
.groovy-icon:before        { @include file-icon; content: "\e904"; top: 4px; font-size: 17px; left: -1px; }
.grunt-icon:before         { @include file-icon; content: "\e611"; top: 1px; font-size: 14px; }
.gulp-icon:before          { @include file-icon; content: "\e610"; top: 2px; font-size: 16px; }
.hack-icon:before          { @include file-icon; content: "\e9ce"; top: 2px; }
.haml-icon:before          { @include file-icon; content: "\f15b"; top: 2px; }
.hoplon-icon:before        { @include file-icon; content: "\ea4d"; top: 2px; }
.harbour-icon:before       { @include file-icon; content: "\e97b"; top: 2px; font-size: 16px; @include sharpen; }
.hashicorp-icon:before     { @include file-icon; content: "\e97e"; top: 2px; }
.haxe-icon:before          { @include file-icon; content: "\e907"; top: 2px; }
.haxedevelop-icon:before   { @include file-icon; content: "\ea3b"; top: 2px; font-size: 16px; @include thicken; }
.helix-icon:before         { @include file-icon; content: "\2695"; top: 2px; font-size: 16px; }
.hg-icon:before            { @include file-icon; content: "\263f"; top: 2px; }
.hjson-icon:before         { @include file-icon; content: "\eac2"; top: 2px; font-size: 16px; @include thicken; }
.houndci-icon:before       { @include file-icon; content: "\eaaf"; top: 2px; }
.hp-icon:before            { @include file-icon; content: "\33cb"; top: 2px; }
.hy-icon:before            { @include file-icon; content: "\e97f"; top: 2px; }
.hyper-icon:before         { @include file-icon; content: "\eb37"; top: 2px; }
.kx-icon:before            { @include file-icon; content: "\ea9b"; top: 2px; }
.husky-icon:before         { @include file-icon; content: "\1f436";top: 2px; font-size: 17px; @include sharpen; }
.icomoon-icon:before       { @include file-icon; content: "\eaea"; top: 2px; }
.idl-icon:before           { @include file-icon; content: "\e947"; top: 3px; font-size: 18px; }
.idris-icon:before         { @include file-icon; content: "\e983"; top: 2px; font-size: 16px; @include thicken; }
.igorpro-icon:before       { @include file-icon; content: "\e980"; top: 2px; font-size: 16px; @include thicken; }
.indesign-icon:before      { @include file-icon; content: "\e9f4"; top: 2px; }
.infopath-icon:before      { @include file-icon; content: "\ea35"; top: 2px; font-size: 16px; }
.inform7-icon:before       { @include file-icon; content: "\e984"; top: 2px; font-size: 16px; @include sharpen; }
.inkscape-icon:before      { @include file-icon; content: "\ea8e"; top: 3px; font-size: 16px; }
.inno-icon:before          { @include file-icon; content: "\e985"; top: 2px; }
.imba-icon:before          { @include file-icon; content: "\26ff"; top: 2px; font-size: 16px; }
.ink-icon:before           { @include file-icon; content: "\eace"; top: 2px; font-size: 16px; }
.io-icon:before            { @include file-icon; content: "\e981"; top: 1px; font-size: 13px; @include thicken; }
.ioke-icon:before          { @include file-icon; content: "\e982"; top: 2px; }
.ionic-icon:before         { @include file-icon; content: "\f14b"; top: 2px; }
.isabelle-icon:before      { @include file-icon; content: "\e945"; top: 2px; font-size: 16px; }
.istanbul-icon:before      { @include file-icon; content: "\1f54c";top: 2px; font-size: 17px; }
.j-icon:before             { @include file-icon; content: "\e937"; top: 1px; font-size: 13px; }
.jade-icon:before          { @include file-icon; content: "\e90d"; top: 1px; font-size: 14px; }
.jake-icon:before          { @include file-icon; content: "\e948"; top: 3px; font-size: 16px; }
.janet-icon:before         { @include file-icon; content: "\eb2a"; top: 2px; font-size: 17px; }
.jasmine-icon:before       { @include file-icon; content: "\ea3a"; top: 3px; font-size: 16px; }
.jenkins-icon:before       { @include file-icon; content: "\e667"; top: 3px; font-size: 18px; @include sharpen; }
.jest-icon:before          { @include file-icon; content: "\ea39"; top: 2px; }
.jolie-icon:before         { @include file-icon; content: "\ea75"; top: 2px; }
.jinja-icon:before         { @include file-icon; content: "\e944"; top: 2px; }
.jison-icon:before         { @include file-icon; content: "\ea55"; top: 2px; }
.json-icon:before          { @include file-icon; content: "\eabe"; top: 2px; }
.json5-icon:before         { @include file-icon; content: "\2478"; top: 2px; }
.jsonld-icon:before        { @include file-icon; content: "\e958"; top: 3px; font-size: 17px; }
.jsonnet-icon:before       { @include file-icon; content: "\eb1e"; top: 3px; font-size: 16px; }
.jsx-icon:before           { @include file-icon; content: "\e9e6"; top: 1px; font-size: 14px; }
.julia-icon:before         { @include file-icon; content: "\26ec"; top: 1px; font-size: 14px; }
.junos-icon:before         { @include file-icon; content: "\ea81"; top: 2px; }
.jupyter-icon:before       { @include file-icon; content: "\e987"; top: 3px; font-size: 16px; }
.karma-icon:before         { @include file-icon; content: "\e9cd"; top: 2px; }
.keybase-icon:before       { @include file-icon; content: "\eaf8"; top: 2px; font-size: 17px; }
.keynote-icon:before       { @include file-icon; content: "\e9e5"; top: 2px; }
.khronos-icon:before       { @include file-icon; content: "\e9f8"; top: 2px; }
.kicad-icon:before         { @include file-icon; content: "\ea4c"; top: 2px; }
.kitchenci-icon:before     { @include file-icon; content: "\ea38"; top: 2px; }
.kivy-icon:before          { @include file-icon; content: "\e901"; top: 2px; }
.knockout-icon:before      { @include file-icon; content: "\4B";   top: 2px; }
.kos-icon:before           { @include file-icon; content: "k";     top: 4px; font-size: 17px; }
.kotlin-icon:before        { @include file-icon; content: "\e989"; top: 1px; font-size: 14px; }
.krl-icon:before           { @include file-icon; content: "\e988"; top: 1px; font-size: 14px; }
.kubernetes-icon:before    { @include file-icon; content: "\2388"; top: 2px; font-size: 16px; }
.labview-icon:before       { @include file-icon; content: "\e98a"; top: 2px; font-size: 16px; }
.lasso-icon:before         { @include file-icon; content: "\e98c"; top: 2px; left: 1px; }
.leaflet-icon:before       { @include file-icon; content: "\ea07"; top: 2px; }
.lean-icon:before          { @include file-icon; content: "L";     top: 1px; font-size: 13px; }
.lein-icon:before          { @include file-icon; content: "\f105"; top: 3px; font-size: 16px; @include sharpen; transform: scale(1.15); }
.lektor-icon:before        { @include file-icon; content: "\eab9"; top: 2px; font-size: 16px; }
.lerna-icon:before         { @include file-icon; content: "\ea37"; top: 2px; font-size: 16px; transform: scale(1.15); }
.lfe-icon:before           { @include file-icon; content: "\e94c"; top: 2px; font-size: 16px; }
.lightwave-icon:before     { @include file-icon; content: "\e9fb"; top: 2px; }
.lime-icon:before          { @include file-icon; content: "\ea36"; top: 2px; font-size: 16px; }
.lisp-icon:before          { @include file-icon; content: "\e908"; top: 3px; font-size: 17px; }
.llvm-icon:before          { @include file-icon; content: "\e91d"; top: 3px; font-size: 17px; }
.logtalk-icon:before       { @include file-icon; content: "\e98d"; top: 2px; @include sharpen; }
.lolcode-icon:before       { @include file-icon; content: "\1f63a";top: 2px; font-size: 16px; @include sharpen; }
.lookml-icon:before        { @include file-icon; content: "\e98e"; top: 2px; font-size: 16px; @include sharpen; }
.ls-icon:before            { @include file-icon; content: "\e914"; top: 2px; font-size: 14px; }
.lsl-icon:before           { @include file-icon; content: "\e98b"; top: 1px; }
.lua-icon:before           { @include file-icon; content: "\e91b"; top: 2px; font-size: 14px; }
.lync-icon:before          { @include file-icon; content: "\ead9"; top: 2px; font-size: 16px; }
.mako-icon:before          { @include file-icon; content: "\e98f"; top: 4px; font-size: 16px; }
.manpage-icon:before       { @include file-icon; content: "\e936"; top: 3px; }
.mapbox-icon:before        { @include file-icon; content: "\e941"; top: 1px; font-size: 13px; }
.marko-icon:before         { @include file-icon; content: "\e920"; top: 4px; font-size: 18px; left: -1px; transform: scale(1.05); }
.markdownlint-icon:before  { @include file-icon; content: "\f0c9"; top: 3px; font-size: 17px; transform: scale(1.25); transform-origin: 0 60%; }
.mathematica-icon:before   { @include file-icon; content: "\e990"; top: 2px; font-size: 16px; }
.mathjax-icon:before       { @include file-icon; content: "\ea06"; top: 2px; }
.matlab-icon:before        { @include file-icon; content: "\e991"; top: 2px; }
.matroska-icon:before      { @include file-icon; content: "\2668"; top: 2px; }
.max-icon:before           { @include file-icon; content: "\e993"; top: 2px; }
.maxscript-icon:before     { @include file-icon; content: "\e900"; top: 2px; }
.maya-icon:before          { @include file-icon; content: "\e9f6"; top: 2px; font-size: 16px; }
.mdx-icon:before           { @include file-icon; content: "\eab7"; top: 3px; font-size: 16px; }
.mediawiki-icon:before     { @include file-icon; content: "\e954"; top: 2px; font-size: 16px; }
.melpa-icon:before         { @include file-icon; content: "\33ab"; top: 2px; font-size: 16px; }
.mercury-icon:before       { @include file-icon; content: "\e994"; top: 3px; font-size: 16px; transform: scale(1.2); }
.meson-icon:before         { @include file-icon; content: "\ea54"; top: 2px; }
.metal-icon:before         { @include file-icon; content: "M";     top: 1px; left: 1px; }
.meteor-icon:before        { @include file-icon; content: "\e6a5"; top: 1px; }
.minecraft-icon:before     { @include file-icon; content: "\e9dc"; top: 2px; }
.minizinc-icon:before      { @include file-icon; content: "\ea53"; top: 2px; }
.mirah-icon:before         { @include file-icon; content: "\e995"; top: 2px; }
.miranda-icon:before       { @include file-icon; content: "\ea52"; top: 3px; font-size: 16px; }
.mirc-icon:before          { @include file-icon; content: "\eb3a"; top: 2px; font-size: 16px; }
.mjml-icon:before          { @include file-icon; content: "\ea6f"; top: 2px; }
.mocha-icon:before         { @include file-icon; content: "\26fe"; top: 2px; font-size: 17px; }
.model-icon:before         { @include file-icon; content: "\e9e8"; top: 2px; font-size: 16px; }
.modula2-icon:before       { @include file-icon; content: "\e996"; top: 2px; }
.modula3-icon:before       { @include file-icon; content: "\2778"; top: 3px; font-size: 17px; }
.modelica-icon:before      { @include file-icon; content: "\eaff"; top: 4px; font-size: 17px; transform: scale(1.2); }
.moho-icon:before          { @include file-icon; content: "\eaee"; top: 3px; font-size: 16px; }
.moleculer-icon:before     { @include file-icon; content: "\eb0e"; top: 3px; font-size: 16px; }
.moment-icon:before        { @include file-icon; content: "\1f558";top: 2px; }
.moment-tz-icon:before     { @include file-icon; content: "\1f30d";top: 2px; }
.monotone-icon:before      { @include file-icon; content: "\1f400";top: 2px; font-size: 18px; }
.monkey-icon:before        { @include file-icon; content: "\e997"; top: 3px; font-size: 18px; left: -1px; }
.mruby-icon:before         { @include file-icon; content: "\ea18"; top: 2px; }
.msproject-icon:before     { @include file-icon; content: "\eae8"; top: 2px; }
.mupad-icon:before         { @include file-icon; content: "\e9ca"; top: 3px; font-size: 16px; }
.mustache-icon:before      { @include file-icon; content: "\e60f"; top: 2px; font-size: 16px; }
.n64-icon:before           { @include file-icon; content: "n";     top: 2px; font-size: 16px; }
.nailpolish-icon:before    { @include file-icon; content: "\1f485";top: 3px; font-size: 16px; left: 1px; }
.nano-icon:before          { @include file-icon; content: "\ea76"; top: 2px; }
.nanoc-icon:before         { @include file-icon; content: "\ea51"; top: 2px; }
.nant-icon:before          { @include file-icon; content: "\e9e1"; top: 3px; transform: scale(1.2); }
.nasm-icon:before          { @include file-icon; content: "\ea72"; top: 2px; }
.ndepend-icon:before       { @include file-icon; content: "\eab4"; top: 2px; }
.neko-icon:before          { @include file-icon; content: "\ea05"; top: 2px; }
.neo4j-icon:before         { @include file-icon; content: "\eab3"; top: 2px; }
.netlify-icon:before       { @include file-icon; content: "\eabf"; top: 3px; font-size: 16px; }
.netlogo-icon:before       { @include file-icon; content: "\e99c"; top: 2px; left: 1px; }
.newrelic-icon:before      { @include file-icon; content: "\e9d7"; top: 2px; }
.nextflow-icon:before      { @include file-icon; content: "\eaa5"; top: 2px; font-size: 16px; }
.nestjs-icon:before        { @include file-icon; content: "\eac9"; top: 2px; }
.nginx-icon:before         { @include file-icon; content:"\f146b"; top: 2px; }
.nib-icon:before           { @include file-icon; content: "\2712"; top: 2px; }
.nightwatch-icon:before    { @include file-icon; content: "\eb08"; top: 3px; font-size: 16px; }
.nimrod-icon:before        { @include file-icon; content: "\e998"; top: 2px; }
.nit-icon:before           { @include file-icon; content: "\e999"; top: 2px; }
.nix-icon:before           { @include file-icon; content: "\e99a"; top: 3px; font-size: 16px; }
.nmap-icon:before          { @include file-icon; content: "\e94d"; top: 3px; font-size: 16px; transform: scale(1.1); }
.nodemon-icon:before       { @include file-icon; content: "\ea26"; top: 2px; }
.nomad-icon:before         { @include file-icon; content: "\ea96"; top: 2px; }
.noon-icon:before          { @include file-icon; content: "\eb33"; top: 2px; }
.normalize-icon:before     { @include file-icon; content: "\ea04"; top: 3px; font-size: 16px; }
.npm-icon:before           { @include file-icon; content: "\e91c"; top: 3px; font-size: 17px; }
.nsis-icon:before          { @include file-icon; content: "\ea1e"; top: 3px; font-size: 16px; }
.nsri-icon:before          { @include file-icon; content: "\eb2f"; top: 2px; }
.nsri-alt-icon:before      { @include file-icon; content: "\eb2b"; top: 2px; font-size: 16px; }
.nuclide-icon:before       { @include file-icon; content: "\ea34"; top: 2px; }
.nuget-icon:before         { @include file-icon; content: "\e9d9"; top: 2px; }
.numpy-icon:before         { @include file-icon; content: "\e99d"; top: 2px; font-size: 14px; }
.nunjucks-icon:before      { @include file-icon; content: "\e953"; top: 2px; font-size: 16px; }
.nuxt-icon:before          { @include file-icon; content: "\eaca"; top: 2px; font-size: 16px; }
.nxc-icon:before           { @include file-icon; content: "\ea6b"; top: 2px; font-size: 16px; }
.nvidia-icon:before        { @include file-icon; content: "\e95d"; top: 2px; }
.oberon-icon:before        { @include file-icon; content: "\eb3f"; top: 3px; font-size: 17px; transform: scale(1.1); }
.objj-icon:before          { @include file-icon; content: "\e99e"; top: 2px; }
.ocaml-icon:before         { @include file-icon; content: "\e91a"; top: 1px; font-size: 14px; }
.octave-icon:before        { @include file-icon; content: "\ea33"; top: 2px; }
.odin-icon:before          { @include file-icon; content: "\eb36"; top: 2px; }
.onenote-icon:before       { @include file-icon; content: "\e9eb"; top: 2px; }
.ooc-icon:before           { @include file-icon; content: "\e9cb"; top: 2px; }
.opa-icon:before           { @include file-icon; content: "\2601"; top: 2px; }
.opencl-icon:before        { @include file-icon; content: "\e99f"; top: 2px; font-size: 16px; }
.opengl-icon:before        { @include file-icon; content: "\ea7a"; top: 3px; font-size: 18px; transform: scale(1.1); }
.openoffice-icon:before    { @include file-icon; content: "\e9e4"; top: 2px; }
.openpolicy-icon:before    { @include file-icon; content: "\eb39"; top: 2px; font-size: 16px; }
.openvms-icon:before       { @include file-icon; content: "\eac6"; top: 1px; font-size: 16px; transform: scale(1.1); }
.openvpn-icon:before       { @include file-icon; content: "\eaf3"; top: 2px; }
.org-icon:before           { @include file-icon; content: "\e917"; top: 1px; font-size: 14px; left: 1px; }
.outlook-icon:before       { @include file-icon; content: "\eada"; top: 2px; font-size: 16px; }
.owl-icon:before           { @include file-icon; content: "\e957"; top: 2px; }
.ox-icon:before            { @include file-icon; content: "\e9a1"; top: 3px; font-size: 16px; @include sharpen; }
.oxygene-icon:before       { @include file-icon; content: "\e9bf"; top: 2px; }
.oz-icon:before            { @include file-icon; content: "\e9be"; top: 2px; }
.p4-icon:before            { @include file-icon; content: "\ea50"; top: 2px; }
.pan-icon:before           { @include file-icon; content: "\e9bd"; top: 2px; }
.papyrus-icon:before       { @include file-icon; content: "\e9bc"; top: 2px; }
.parrot-icon:before        { @include file-icon; content: "\e9bb"; top: 3px; font-size: 16px; }
.pascal-icon:before        { @include file-icon; content: "\e92a"; top: 2px; }
.patch-icon:before         { @include file-icon; content: "\e961"; top: 2px; }
.patreon-icon:before       { @include file-icon; content: "\eb42"; top: 2px; }
.pawn-icon:before          { @include file-icon; content: "\265f"; top: 1px; font-size: 14px; }
.pcd-icon:before           { @include file-icon; content: "\26c5"; top: 2px; font-size: 16px; }
.peg-icon:before           { @include file-icon; content: "\ea74"; top: 3px; font-size: 16px; }
.perl6-icon:before         { @include file-icon; content: "\e96c"; top: 2px; }
.phalcon-icon:before       { @include file-icon; content: "\e94a"; top: 2px; }
.phoenix-icon:before       { @include file-icon; content: "\ea5f"; top: 3px; font-size: 17px; transform: scale(1.1); }
.php-icon:before           { @include file-icon; content: "\f147"; top: 1px; font-size: 14px; left: 1px; }
.phpunit-icon:before       { @include file-icon; content: "\ea32"; top: 2px; }
.phraseapp-icon:before     { @include file-icon; content: "\eadb"; top: 2px; }
.pickle-icon:before        { @include file-icon; content: "\e9c4"; top: 2px; }
.pico8-icon:before         { @include file-icon; content: "\eabc"; top: 2px; }
.picolisp-icon:before      { @include file-icon; content: "\eb25"; top: 3px; font-size: 17px; }
.pike-icon:before          { @include file-icon; content: "\e9b9"; top: 4px; font-size: 16px; @include thicken; transform: scale(1.15); }
.pinescript-icon:before    { @include file-icon; content: "\eb30"; top: 2px; }
.pipenv-icon:before        { @include file-icon; content: "\1f381";top: 2px; }
.platformio-icon:before    { @include file-icon; content: "\ea2c"; top: 2px; font-size: 16px; }
.pm2-icon:before           { @include file-icon; content: "\2630"; top: 3px; }
.pod-icon:before           { @include file-icon; content: "\ea84"; top: 2px; left: 1px; }
.pogo-icon:before          { @include file-icon; content: "\e9b8"; top: 3px; font-size: 14px; @include thicken; }
.pointwise-icon:before     { @include file-icon; content: "\e977"; top: 2px; }
.polymer-icon:before       { @include file-icon; content: "\ea2b"; top: 3px; }
.pony-icon:before          { @include file-icon; content: "\e9b7"; top: 3px; font-size: 16px; }
.postcss-icon:before       { @include file-icon; content: "\e910"; top: 2px; font-size: 14px; }
.postscript-icon:before    { @include file-icon; content: "\e955"; top: 2px; left: 1px; }
.povray-icon:before        { @include file-icon; content: "P";     top: 2px; left: 1px; }
.powerbuilder-icon:before  { @include file-icon; content: "\ea14"; }
.powerpoint-icon:before    { @include file-icon; content: "\e9ec"; top: 2px; }
.powershell-icon:before    { @include file-icon; content: "\e9da"; top: 2px; font-size: 16px; }
.precommit-icon:before     { @include file-icon; content: "\eac1"; top: 2px; }
.premiere-icon:before      { @include file-icon; content: "\e9f5"; top: 2px; }
.prettier-icon:before      { @include file-icon; content: "\eaa1"; top: 2px; }
.prisma-icon:before        { @include file-icon; content: "\eac5"; top: 2px; }
.processing-icon:before    { @include file-icon; content: "\e9a0"; top: 2px; }
.progress-icon:before      { @include file-icon; content: "\eadc"; top: 2px; }
.proselint-icon:before     { @include file-icon; content: "\ea6d"; top: 2px; font-size: 16px; }
.pros-icon:before          { @include file-icon; content: "\eaad"; top: 3px; font-size: 16px; }
.propeller-icon:before     { @include file-icon; content: "\e9b5"; top: 3px; font-size: 16px; }
.protractor-icon:before    { @include file-icon; content: "\e9de"; top: 3px; }
.psd-icon:before           { @include file-icon; content: "\e6b8"; top: 2px; }
.publisher-icon:before     { @include file-icon; content: "\eadd"; top: 2px; font-size: 16px; }
.pug-alt-icon:before       { @include file-icon; content: "\e9d0"; top: 3px; font-size: 16px; }
.pug-icon:before           { @include file-icon; content: "\ea13"; top: 3px; font-size: 16px; }
.puppet-icon:before        { @include file-icon; content: "\f0c3"; top: 2px; left: 1px; }
.purebasic-icon:before     { @include file-icon; content: "\01b5"; top: 2px; }
.purescript-icon:before    { @include file-icon; content: "\e9b2"; top: 3px; }
.pullapprove-icon:before   { @include file-icon; content: "\293e"; top: 2px; }
.pypi-icon:before          { @include file-icon; content: "\ea94"; top: 2px; }
.pyret-icon:before         { @include file-icon; content: "\2620"; top: 2px; font-size: 16px; }
.pyup-icon:before          { @include file-icon; content: "\eb26"; top: 2px; }
.qlik-icon:before          { @include file-icon; content: "\1f50d";top: 2px; }
.qt-icon:before            { @include file-icon; content: "\eb00"; top: 2px; font-size: 16px; }
.quasar-icon:before        { @include file-icon; content: "\eacc"; top: 2px; }
.r-icon:before             { @include file-icon; content: "\e905"; top: 3px; font-size: 17px; }
.racket-icon:before        { @include file-icon; content: "\e9b1"; top: 2px; left: 1px; }
.raml-icon:before          { @include file-icon; content: "\e913"; top: 1px; font-size: 14px; }
.rascal-icon:before        { @include file-icon; content: "\ea24"; top: 2px; }
.razzle-icon:before        { @include file-icon; content: "R";     top: 2px; }
.rdoc-icon:before          { @include file-icon; content: "\e9b0"; top: 2px; left: 1px; }
.react-icon:before         { @include file-icon; content: "\f100"; top: 2px; }
.reason-icon:before        { @include file-icon; content: "\ea1d"; top: 3px; }
.rebol-icon:before         { @include file-icon; content: "\e9ae"; top: 1px; font-size: 13px; }
.red-icon:before           { @include file-icon; content: "\eaec"; top: 3px; font-size: 16px; }
.redux-icon:before         { @include file-icon; content: "\ea30"; top: 2px; }
.reek-icon:before          { @include file-icon; content: "\eaa0"; top: 3px; font-size: 17px; @include sharpen; }
.regex-icon:before         { @include file-icon; content: "*";     top: 1px; font-size: 12px; left: 1px; }
.remark-icon:before        { @include file-icon; content: "\eb1d"; top: 2px; }
.renovate-icon:before      { @include file-icon; content: "\eb2d"; top: 2px; font-size: 17px; }
.rexx-icon:before          { @include file-icon; content: "\ea16"; top: 2px; font-size: 14px; left: 1px; }
.rhino-icon:before         { @include file-icon; content: "\ea4a"; top: 4px; font-size: 16px; left: 1px; transform: scale(1.1); }
.ring-icon:before          { @include file-icon; content: "\1f48d";top: 2px; }
.riot-icon:before          { @include file-icon; content: "\eb2e"; top: 2px; }
.robot-icon:before         { @include file-icon; content: "\eb0d"; top: 2px; }
.robots-icon:before        { @include file-icon; content: "\1f916";top: 2px; }
.rollup-icon:before        { @include file-icon; content: "\ea20"; top: 2px; }
.rspec-icon:before         { @include file-icon; content: "\ea31"; top: 3px; font-size: 16px; }
.rst-icon:before           { @include file-icon; content: "\e9cc"; top: 3px; font-size: 16px; }
.rstudio-icon:before       { @include file-icon; content: "\24c7"; top: 2px; }
.rubocop-icon:before       { @include file-icon; content: "\eade"; top: 2px; }
.sage-icon:before          { @include file-icon; content: "\e9ab"; top: 3px; font-size: 16px; @include thicken; }
.saltstack-icon:before     { @include file-icon; content: "\e915"; top: 2px; font-size: 14px; }
.sas-icon:before           { @include file-icon; content: "\e95a"; top: 2px; }
.san-icon:before           { @include file-icon; content: "\eae6"; top: 2px; }
.sbt-icon:before           { @include file-icon; content: "\e9d2"; top: 2px; font-size: 14px; }
.scad-icon:before          { @include file-icon; content: "\e911"; top: 2px; font-size: 14px; }
.scd-icon:before           { @include file-icon; content: "\e9a2"; top: 2px; }
.scheme-icon:before        { @include file-icon; content: "\03bb"; top: 2px; }
.scilab-icon:before        { @include file-icon; content: "\e9a9"; top: 3px; font-size: 18px; left: -1px; @include thicken; }
.scilla-icon:before        { @include file-icon; content: "\eae2"; top: 3px; font-size: 16px; }
.scratch-icon:before       { @include file-icon; content: "\a7";   top: 2px; }
.scrutinizer-icon:before   { @include file-icon; content: "\e9d4"; top: 2px; font-size: 14px; }
.self-icon:before          { @include file-icon; content: "\e9a8"; top: 3px; font-size: 16px; @include sharpen; transform: scale(1.2); }
.sentry-icon:before        { @include file-icon; content: "\eb1c"; top: 3px; font-size: 16px; }
.serverless-icon:before    { @include file-icon; content: "\eab8"; top: 2px; }
.sequelize-icon:before     { @include file-icon; content: "\ea2f"; top: 3px; font-size: 16px; }
.sf-icon:before            { @include file-icon; content: "\e9db"; top: 2px; }
.sgi-icon:before           { @include file-icon; content: "\2318"; top: 2px; }
.shen-icon:before          { @include file-icon; content: "\e9a7"; top: 2px; font-size: 16px; }
.shipit-icon:before        { @include file-icon; content: "\26f5"; top: 2px; font-size: 16px; transform: scale(1.1); }
.shippable-icon:before     { @include file-icon; content: "\ea2d"; top: 2px; }
.shopify-icon:before       { @include file-icon; content: "\e9cf"; top: 2px; }
.shuriken-icon:before      { @include file-icon; content: "\272b"; top: 2px; font-size: 14px; }
.sigils-icon:before        { @include file-icon; content: "\1f764";top: 3px; font-size: 16px; @include sharpen; }
.silverstripe-icon:before  { @include file-icon; content: "\e800"; top: 2px; }
.sketch-icon:before        { @include file-icon; content: "\e927"; top: 2px; }
.sketchup-lo-icon:before   { @include file-icon; content: "\ea7c"; top: 2px; }
.sketchup-mk-icon:before   { @include file-icon; content: "\ea7e"; top: 2px; }
.sketchup-sb-icon:before   { @include file-icon; content: "\ea7d"; top: 2px; }
.slash-icon:before         { @include file-icon; content: "\e9a6"; top: 2px; }
.snapcraft-icon:before     { @include file-icon; content: "\eb09"; top: 3px; font-size: 16px; }
.snort-icon:before         { @include file-icon; content: "\1f43d";top: 3px; font-size: 17px; }
.snyk-icon:before          { @include file-icon; content: "\ea1c"; top: 2px; font-size: 16px; }
.solidarity-icon:before    { @include file-icon; content: "\1d5e6";top: 2px; }
.solidity-icon:before      { @include file-icon; content: "\ea86"; top: 2px; }
.sorbet-icon:before        { @include file-icon; content: "\1f366";top: 2px; font-size: 16px; }
.spacemacs-icon:before     { @include file-icon; content: "\eaa4"; top: 2px; }
.sparql-icon:before        { @include file-icon; content: "\e959"; top: 2px; }
.sqf-icon:before           { @include file-icon; content: "\e9a5"; top: 1px; @include sharpen; }
.sqlite-icon:before        { @include file-icon; content: "\e9dd"; top: 3px; }
.squarespace-icon:before   { @include file-icon; content: "\ea5e"; top: 2px; }
.stan-icon:before          { @include file-icon; content: "\e9a4"; top: 2px; }
.stata-icon:before         { @include file-icon; content: "\e9a3"; top: 2px; }
.stencil-icon:before       { @include file-icon; content: "\ea95"; top: 2px; }
.storyist-icon:before      { @include file-icon; content: "\e9ef"; top: 2px; font-size: 16px; }
.strings-icon:before       { @include file-icon; content: "\e9e0"; top: 2px; }
.stylable-icon:before      { @include file-icon; content: "\eae0"; top: 2px; }
.storybook-icon:before     { @include file-icon; content: "\eadf"; top: 2px; font-size: 16px; }
.stylelint-icon:before     { @include file-icon; content: "\e93d"; top: 2px; }
.stylishhaskell-icon:before{ @include file-icon; content: "\eb3d"; top: 3px; font-size: 16px; }
.stylus-icon:before        { @include file-icon; content: "s";     top: 2px; left: 1px; }
.sublime-icon:before       { @include file-icon; content: "\e986"; top: 2px; }
.svelte-icon:before        { @include file-icon; content: "\33dc"; top: 3px; font-size: 16px; }
.svn-icon:before           { @include file-icon; content: "\ea17"; top: 2px; }
.swagger-icon:before       { @include file-icon; content: "\ea29"; top: 2px; }
.sysverilog-icon:before    { @include file-icon; content: "\e9c3"; top: 2px; }
.tag-icon:before           { @include file-icon; content: "\f015"; top: 2px; font-size: 14px; }
.tailwind-icon:before      { @include file-icon; content: "\301c"; top: 3px; font-size: 17px; }
.tcl-icon:before           { @include file-icon; content: "\e956"; top: 2px; font-size: 16px; }
.telegram-icon:before      { @include file-icon; content: "\2708"; top: 2px; }
.templeos-icon:before      { @include file-icon; content: "\2696"; top: 4px; font-size: 18px; }
.terminal-icon:before      { @include file-icon; content: "\f0c8"; top: 2px; font-size: 14px; }
.tern-icon:before          { @include file-icon; content: "\1f54a";top: 4px; font-size: 16px; }
.terraform-icon:before     { @include file-icon; content: "\e916"; top: 1px; font-size: 14px; }
.terser-icon:before        { @include file-icon; content: "\272c"; top: 2px; }
.test-coffee-icon:before   { @include file-icon; content: "\ea62"; @include test-file; }
.test-dir-icon:before      { @include file-icon; content: "\ea60"; top: 2px; }
.test-generic-icon:before  { @include file-icon; content: "\ea63"; @include test-file; }
.test-go-icon:before       { @include file-icon; content: "\eb0f"; @include test-file; }
.test-hs-icon:before       { @include file-icon; content: "\eb10"; @include test-file; }
.test-js-icon:before       { @include file-icon; content: "\ea64"; @include test-file; }
.test-perl-icon:before     { @include file-icon; content: "\ea65"; @include test-file; }
.test-python-icon:before   { @include file-icon; content: "\ea66"; @include test-file; }
.test-react-icon:before    { @include file-icon; content: "\ea67"; @include test-file; }
.test-ruby-icon:before     { @include file-icon; content: "\ea68"; @include test-file; }
.test-rust-icon:before     { @include file-icon; content: "\eb11"; @include test-file; }
.test-ts-icon:before       { @include file-icon; content: "\ea69"; @include test-file; }
.tex-icon:before           { @include file-icon; content: "\e600"; top: 4px; font-size: 16px; @include thicken; }
.textile-icon:before       { @include file-icon; content: "t";     top: 2px; }
.textmate-icon:before      { @include file-icon; content: "\2122"; top: 2px; font-size: 16px; }
.tfs-icon:before           { @include file-icon; content: "\eae1"; top: 3px; }
.thor-icon:before          { @include file-icon; content: "\e9d8"; top: 2px; }
.tipe-icon:before          { @include file-icon; content: "\eaa9"; top: 3px; font-size: 16px; }
.tla-icon:before           { @include file-icon; content: "\eab2"; top: 2px; }
.tmux-icon:before          { @include file-icon; content: "\ea8c"; top: 2px; }
.toml-icon:before          { @include file-icon; content: "\1f143";top: 2px; }
.tortoise-icon:before      { @include file-icon; content: "\ea93"; top: 3px; font-size: 17px; transform: scale(1.15); }
.totvs-icon:before         { @include file-icon; content: "\eb34"; top: 2px; }
.truffle-icon:before       { @include file-icon; content: "\eb3b"; top: 2px; }
.ts-icon:before            { @include file-icon; content: "\2a6";  top: 1px; font-size: 14px; }
.tsx-icon:before           { @include file-icon; content: "\e9e7"; top: 1px; font-size: 14px; }
.tt-icon:before            { @include file-icon; content: "T";     top: 2px; }
.ttcn3-icon:before         { @include file-icon; content: "\2476"; top: 2px; }
.turing-icon:before        { @include file-icon; content: "\e9b6"; top: 2px; }
.twine-icon:before         { @include file-icon; content: "\ea5d"; top: 2px; }
.twig-icon:before          { @include file-icon; content: "\2e19"; top: 2px; font-size: 16px; @include sharpen; }
.txl-icon:before           { @include file-icon; content: "\e9c1"; top: 2px; }
.typedoc-icon:before       { @include file-icon; content: "\e9fe"; top: 2px; }
.typings-icon:before       { @include file-icon; content: "\e9df"; top: 2px; }
.unbeautify-icon:before    { @include file-icon; content: "\eac4"; top: 2px; font-size: 16px; @include sharpen; }
.uno-icon:before           { @include file-icon; content: "\e9b3"; top: 2px; }
.unreal-icon:before        { @include file-icon; content: "u";     top: 2px; }
.urweb-icon:before         { @include file-icon; content: "\e9ba"; top: 4px; font-size: 18px; left: -1px; @include sharpen; }
.v-icon:before             { @include file-icon; content: "v";     top: 2px; }
.v8-icon:before            { @include file-icon; content: "\ea1f"; top: 3px; font-size: 16px; }
.v8-turbofan-icon:before   { @include file-icon; content: "\eaac"; top: 3px; font-size: 17px; }
.vagrant-icon:before       { @include file-icon; content: "V";     top: 2px; font-size: 14px; }
.varnish-icon:before       { @include file-icon; content: "\e9b4"; top: 1px; font-size: 14px; }
.velocity-icon:before      { @include file-icon; content: "\2b94"; top: 3px; font-size: 17px; transform: scale(1.1); }
.verilog-icon:before       { @include file-icon; content: "\e949"; top: 2px; }
.vertex-icon:before        { @include file-icon; content: "\ea79"; top: 2px; font-size: 16px; }
.vhdl-icon:before          { @include file-icon; content: "\e9aa"; top: 2px; }
.video-icon:before         { @include file-icon; content: "\f057"; top: 1px; font-size: 14px; }
.virtualbox-icon:before    { @include file-icon; content: "\ea3e"; top: 2px; font-size: 16px; }
.visio-icon:before         { @include file-icon; content: "\ea83"; top: 2px; }
.vmware-icon:before        { @include file-icon; content: "\ea49"; top: 3px; font-size: 16px; @include sharpen; }
.vue-icon:before           { @include file-icon; content: "\e906"; top: 3px; }
.vyper-icon:before         { @include file-icon; content: "\eb31"; top: 3px; font-size: 16px; }
.vsts-icon:before          { @include file-icon; content: "\eac0"; top: 3px; font-size: 16px; left: -1px; }
.wallaby-icon:before       { @include file-icon; content: "\231f"; top: 2px; }
.walt-icon:before          { @include file-icon; content: "\eaba"; top: 3px; font-size: 16px; }
.wasm-icon:before          { @include file-icon; content: "\ea70"; top: 2px; }
.watchman-icon:before      { @include file-icon; content: "\ea4f"; top: 2px; }
.wdl-icon:before           { @include file-icon; content: "\eab1"; top: 2px; }
.webgl-icon:before         { @include file-icon; content: "\ea7b"; top: 3px; font-size: 18px; transform: scale(1.1); }
.webhint-icon:before       { @include file-icon; content: "\eb35"; top: 2px; font-size: 16px; }
.webpack-icon:before       { @include file-icon; content: "\ea61"; top: 3px; font-size: 16px; }
.webvtt-icon:before        { @include file-icon; content: "\eb24"; top: 4px; font-size: 16px; transform: scale(1.1); }
.wercker-icon:before       { @include file-icon; content: "\ea19"; top: 2px; }
.wget-icon:before          { @include file-icon; content: "\eb38"; top: 2px; }
.wix-icon:before           { @include file-icon; content: "\eab0"; top: 3px; font-size: 16px; }
.word-icon:before          { @include file-icon; content: "\e9ed"; top: 2px; }
.workbox-icon:before       { @include file-icon; content: "\eaa8"; top: 2px; }
.wurst-icon:before         { @include file-icon; content: "\1f32d";top: 2px; font-size: 16px; }
.xamarin-icon:before       { @include file-icon; content: "\ea77"; top: 2px; }
.x10-icon:before           { @include file-icon; content: "\2169"; top: 2px; }
.xmos-icon:before          { @include file-icon; content: "X";     top: 1px; font-size: 14px; }
.xojo-icon:before          { @include file-icon; content: "\e9af"; top: 2px; }
.xpages-icon:before        { @include file-icon; content: "\e9c5"; top: 2px; }
.xtend-icon:before         { @include file-icon; content: "\e9c6"; top: 2px; }
.yaml-icon:before          { @include file-icon; content: "y";     top: 2px; }
.yandex-icon:before        { @include file-icon; content: "\42f";  top: 2px; }
.yang-icon:before          { @include file-icon; content: "\262f"; top: 2px; }
.yara-icon:before          { @include file-icon; content: "\ea9f"; top: 2px; }
.yarn-icon:before          { @include file-icon; content: "\ea1a"; top: 2px; font-size: 16px; }
.yui-icon:before           { @include file-icon; content: "\ea00"; top: 2px; }
.zbrush-icon:before        { @include file-icon; content: "\e9f2"; top: 2px; font-size: 16px; }
.zeit-icon:before          { @include file-icon; content: "\25B2"; top: 2px; }
.zephir-icon:before        { @include file-icon; content: "\e9c7"; top: 2px; @include thicken; }
.zig-icon:before           { @include file-icon; content: "z";     top: 3px; font-size: 16px; }
.zimpl-icon:before         { @include file-icon; content: "\e9c8"; top: 2px; font-size: 16px; left: 1px; }
.zork-icon:before          { @include file-icon; content: "\1b6";  top: 2px; }

@font-face {
	font-family: 'Mfizz';
	font-weight: normal;
	font-style: normal;
	font-display: swap;
	src: local("Mfizz"),
		url('mfixx.woff2');
}

@font-face {
	font-family: 'Octicons Regular';
	font-weight: normal;
	font-style: normal;
	font-display: swap;
	src: local("Octicons Regular"),
		url('octicons.woff2') format("woff2");
}

@font-face {
	font-family: 'Devicons';
	font-weight: normal;
	font-style: normal;
	font-display: swap;
	src: local("Devicons"),
		url('devopicons.woff2') format("woff2");
}

@font-face {
	font-family: 'file-icons';
	font-weight: normal;
	font-style: normal;
	font-display: swap;
	src: local("file-icons"),
		url('file-icons.woff2') format("woff2");
}