<?php

//////////////////////////////////////////////////////////////////////////////80
// Active Controller
//////////////////////////////////////////////////////////////////////////////80
// Copyright (c) 2020 <PERSON> (<EMAIL>), distributed as-is and without
// warranty under the MIT License. See [root]/docs/LICENSE.md for more.
// This information must remain intact.
//////////////////////////////////////////////////////////////////////////////80
// Authors: <AUTHORS>
//////////////////////////////////////////////////////////////////////////////80

require_once('class.sessionmanager.php');

$path = POST("path");

//////////////////////////////////////////////////////////////////////////////80
// Security Check
//////////////////////////////////////////////////////////////////////////////80
$path = Common::getWorkspacePath($path);

//////////////////////////////////////////////////////////////////////////////80
// Handle Action
//////////////////////////////////////////////////////////////////////////////80
$SessionManager = new SessionManager();

switch ($action) {
    //////////////////////////////////////////////////////////////////////////80
    // Add active record
    //////////////////////////////////////////////////////////////////////////80
    case "add":
        $SessionManager->add($path);
        break;

    //////////////////////////////////////////////////////////////////////////80
    // Check if file is active
    //////////////////////////////////////////////////////////////////////////80
    case "check":
        $SessionManager->check($path);
        break;

    //////////////////////////////////////////////////////////////////////////80
    // Get user's active files
    //////////////////////////////////////////////////////////////////////////80
    case "listActiveFiles":
        $SessionManager->listActiveFiles();
        break;

    //////////////////////////////////////////////////////////////////////////80
    // Rename
    //////////////////////////////////////////////////////////////////////////80
    case "rename":
        $newPath = POST("newPath");
        $SessionManager->rename($path, $newPath);
        break;

    //////////////////////////////////////////////////////////////////////////80
    // Remove active record
    //////////////////////////////////////////////////////////////////////////80
    case "remove":
        $SessionManager->remove($path);
        break;

    //////////////////////////////////////////////////////////////////////////80
    // Remove all active record
    //////////////////////////////////////////////////////////////////////////80
    case "removeall":
        $SessionManager->removeAll();
        break;

    //////////////////////////////////////////////////////////////////////////80
    // Mark file as focused
    //////////////////////////////////////////////////////////////////////////80
    case "setFocus":
        $SessionManager->setFocus($path);
        break;

    //////////////////////////////////////////////////////////////////////////80
    // Save
    //////////////////////////////////////////////////////////////////////////80
    case "saveFile":
        $saveType = POST("saveType");
        $newContent = POST("newContent");
        $modifyTime = POST("modifyTime");

        $SessionManager->saveFile($path, $saveType, $newContent, $modifyTime);
        break;

    //////////////////////////////////////////////////////////////////////////80
    // Open
    //////////////////////////////////////////////////////////////////////////80
    case "openFile":
        $inFocus = POST("inFocus");
        $SessionManager->openFile($path, $inFocus);
        break;

    //////////////////////////////////////////////////////////////////////////80
    // Default: Invalid Action
    //////////////////////////////////////////////////////////////////////////80
    default:
        Common::send(416, "Invalid action.");
        break;
}