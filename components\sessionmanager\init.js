//////////////////////////////////////////////////////////////////////////////80
// Session Manager Component
//////////////////////////////////////////////////////////////////////////////80
// Manages the currently active file edit sessions, strictly focused on file
// content.
//////////////////////////////////////////////////////////////////////////////80
// Copyright (c) 2020 Liam <PERSON> (<EMAIL>), distributed as-is and without
// warranty under the MIT License. See [root]/docs/LICENSE.md for more.
// This information must remain intact.
//////////////////////////////////////////////////////////////////////////////80
// Authors: <AUTHORS>
//////////////////////////////////////////////////////////////////////////////80

(function() {
	'use strict';

	var AceEditSession = ace.require('ace/edit_session').EditSession;
	var AceUndoManager = ace.require('ace/undomanager').UndoManager;

	const self = {};

// 	carbon.subscribe('system.loadMajor', () => self.init());
// 	atheos.sessionmanager = self;

})();