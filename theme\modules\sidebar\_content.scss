.sidebar .content {
	position: absolute;
	top: $topBarHeight;
	// min-width: 185px;
	width: calc(100% - #{$sidebarHandleWidth});
	overflow-y: auto;
}

#SBLEFT {
	.content {
		left: 0;
		right: $sidebarHandleWidth;
		bottom: $filetreeHeight;
	}

	&>.content {
		transition: bottom ease-in-out $transitionDuration;
	}
}

#SBRIGHT {
	.content {
		left: $sidebarHandleWidth;
		right: 0;
		height: calc(100% - #{$topBarHeight});
	}

	#last_login {
		display: block;
		margin-bottom: 5px;
	}
}