:root {
	--shade0: #F5F9F9;
	--shade1: #D5D9DD;
	--shade2: #A5A9AA;
	--shade3: #757577;
	--shade4: #414141;
	--shade5: #3A3A3A;
	--shade6: #252525;
	--shade7: #1C1C1E;
	--shade8: #151516;
	--shade9: #010101;
}

html.light {
		--shade0: #010101;
		--shade1: #151516;
		--shade2: #252525;
		--shade3: #3A3A3A;
		--shade4: #414141;
		--shade5: #858587;
		--shade6: #A5A9AA;
		--shade7: #D5D5DD;
		--shade8: #E4E4E9;
		--shade9: #F5F5F9;
}

$shade0: var(--shade0);
$shade1: var(--shade1);
$shade2: var(--shade2);
$shade3: var(--shade3);
$shade4: var(--shade4);
$shade5: var(--shade5);
$shade6: var(--shade6);
$shade7: var(--shade7);
$shade8: var(--shade8);
$shade9: var(--shade9);

//////////////////////////////////////////////////////////////////////////////80
//	CSS CLASSES
//////////////////////////////////////////////////////////////////////////////80
.shade0::before	{ color: $shade0; }
.shade1::before	{ color: $shade1; }
.shade2::before	{ color: $shade2; }
.shade3::before	{ color: $shade3; }
.shade4::before	{ color: $shade4; }
.shade5::before	{ color: $shade5; }
.shade6::before	{ color: $shade6; }
.shade7::before	{ color: $shade7; }
.shade8::before	{ color: $shade8; }
.shade9::before	{ color: $shade9; }