.sidebar {
	.title {
		position: absolute;
		top: 0;
		height: $topBarHeight;
		overflow: hidden;
		background-color: $foreground;
		z-index: 9998;

		h2 {
			font-size: 15px;
			margin: 8px;
			display: inline-block;
		}

		i {
			display: block;
			font-size: 15px;
			color: $fontColorMajor;
			z-index: 99999;
			cursor: pointer;
			margin: 9px 4px;
			float: left;

			&.active,
			&:hover {
				color: $hoverMajor;
			}
		}
	}
}

#SBRIGHT .title {
	right: 0;
	left: $sidebarHandleWidth;

	i {
		float: left;
	}
}

#SBLEFT .title {
	right: $sidebarHandleWidth;
	left: 0;

	i {
		float: right;
	}
}