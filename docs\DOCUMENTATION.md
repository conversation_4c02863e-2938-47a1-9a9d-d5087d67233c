<section>

# Welcome to Atheos Documentation

Atheos is a web-based IDE framework with a small footprint and minimal requirements.

</section>

<section>

This wiki is the main source of documentation for developers working with (or contributing to) the Atheos project.

Need Support? Open an issue here: [Atheos Issues](https://github.com/Atheos/Atheos/issues)

## Using Atheos

*   [Default Hot Keys](/docs/hotkeys)
*   [Workspace Pathing](/docs/workspace-pathing)

## Atheos Documentation

*   [Atheos API: An overview of all Atheos components](/docs/api)
*   [Installation Directions and Tips](/docs/installation)
*   [Updating Atheos](/docs/updating)
*   [Libraries used by Atheos](/docs/libraries)

## Contributing

*   [How To Contribute](/docs/contributing)
*   [Plugins](/docs/contributing/plugins/)
*   [Themes](/docs/contributing/themes)

</section>

<section>

## Authors Ordered By First Contribution

*   <PERSON> - @fluidbyte
*   <PERSON> - @tholum
*   <PERSON><PERSON><PERSON> - @lorefnon
*   <PERSON> - @tablatronix
*   Florent Galland - @Flolagale
*   <PERSON> - @<PERSON><PERSON>
*   <PERSON> - @newsocialifecom
*   <PERSON> D - @daeks
*   <PERSON>-<PERSON> - @holblin
*   <PERSON>ira - @hsliira

and all the other contributors - Thanks!

</section>