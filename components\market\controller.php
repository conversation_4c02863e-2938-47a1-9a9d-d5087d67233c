<?php

//////////////////////////////////////////////////////////////////////////////80
// Market Controller
//////////////////////////////////////////////////////////////////////////////80
// Copyright (c) At<PERSON><PERSON> & <PERSON> (Atheos.io), distributed as-is and without
// warranty under the MIT License. See [root]/docs/LICENSE.md for more.
// This information must remain intact.
//////////////////////////////////////////////////////////////////////////////80
// Authors: <AUTHORS>
//////////////////////////////////////////////////////////////////////////////80

require_once('class.market.php');

if (!Common::checkAccess("configure")) {
	Common::send(403, "User does not have access.");
}

$type = POST("type");
$name = POST("name");
$category = POST("category");
$repo = POST("repo");

$Market = new Market();
//////////////////////////////////////////////////////////////////
// Handle Action
//////////////////////////////////////////////////////////////////
switch ($action) {
	//////////////////////////////////////////////////////////////////
	// Init
	//////////////////////////////////////////////////////////////////
	case 'init':
		$Market->init();
		break;
	//////////////////////////////////////////////////////////////////
	// Install
	//////////////////////////////////////////////////////////////////
	case 'install':
		$Market->install($name, $type, $category);
		break;

	//////////////////////////////////////////////////////////////////
	// Remove
	//////////////////////////////////////////////////////////////////
	case 'remove':
		$Market->remove($name, $type);
		break;

	//////////////////////////////////////////////////////////////////
	// Update
	//////////////////////////////////////////////////////////////////
	case 'update':
		$Market->remove($name, $type);
		$Market->install($name, $type, $category);
		break;

	//////////////////////////////////////////////////////////////////
	// Save Cache
	//////////////////////////////////////////////////////////////////
	case 'saveCache':
		$cache = POST("cache");
		$Market->saveCache($cache);
		break;

	default:
		Common::send(416, "Invalid action.");
		break;
}