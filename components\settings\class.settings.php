<?php

//////////////////////////////////////////////////////////////////////////////80
// Settings Class
//////////////////////////////////////////////////////////////////////////////80
// Copyright (c) 2020 <PERSON> (<EMAIL>), distributed as-is and without
// warranty under the MIT License. See [root]/docs/LICENSE.md for more.
// This information must remain intact.
//////////////////////////////////////////////////////////////////////////////80
// Authors: <AUTHORS>
//////////////////////////////////////////////////////////////////////////////80

class Settings {

	//////////////////////////////////////////////////////////////////////////80
	// PROPERTIES
	//////////////////////////////////////////////////////////////////////////80
	private $activeUser = null;
	private $db = null;

	//////////////////////////////////////////////////////////////////////////80
	// METHODS
	//////////////////////////////////////////////////////////////////////////80

	// ----------------------------------||---------------------------------- //

	//////////////////////////////////////////////////////////////////////////80
	// Construct
	//////////////////////////////////////////////////////////////////////////80
	public function __construct($activeUser) {
		$this->activeUser = $activeUser;
		$this->db = Common::getKeyStore("settings", "users/" . $activeUser);
	}

	//////////////////////////////////////////////////////////////////////////80
	// Load User Settings
	//////////////////////////////////////////////////////////////////////////80
	public function load() {
		$settings = $this->db->select("*");
		if (!empty($settings)) {
			Common::send(200, $settings);
		} else {
			Common::send(404, "Settings for user not found.");
		}
	}

	//////////////////////////////////////////////////////////////////////////80
	// Save User Settings
	//////////////////////////////////////////////////////////////////////////80
	public function save($key, $value) {
		$this->db->update($key, $value, true);
		Common::send(200, "Setting $key saved.");
	}
}