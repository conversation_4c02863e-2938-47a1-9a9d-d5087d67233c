/* CONTEXT MENU */

#contextmenu {
	display: none;
	// top: 0;
	// left: 0;
	position: fixed;
	width: 200px;
	background: var(--mideground);
	z-index: 9999999;
	border: var(--borderLight);
	box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, .9);
	overflow-y: auto;

	a {
		display: block;
		padding: 5px 5px 7px 5px;
		margin: 0;
		user-select: none;

		&:hover {
			background: var(--hoverMinor);
		}
	}

	i {
		// padding-right: 5px;
	}

	hr {
		height: 0;
		border: none;
		border-top: 1px solid #666;
		margin: 3px;
	}

	.disabled {
		color: #999;
		font-style: italic;
	}
}