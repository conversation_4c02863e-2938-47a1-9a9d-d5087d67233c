#macro_editor {
	height: 450px;
	overflow-y: auto;
	overflow-x: hidden;

	tbody {
		display: block;
		max-height: 49vh;
		overflow-y: scroll;
	}

	thead,
	tbody tr {
		display: table;
		table-layout: fixed;
	}

	td {
		padding: 0;

		input,
		toggle {
			margin: 0;
			border-bottom: none;

			&:focus {
				border-bottom: none;
			}
		}
	}

	th:nth-of-type(1),
	td:nth-of-type(1) {
		width: 150px;
		font-weight: 700;
	}

	th:nth-of-type(2),
	td:nth-of-type(2) {
		width: 150px;
	}

	th:nth-of-type(3),
	td:nth-of-type(3) {
		width: 100px;
	}

	th:nth-of-type(4),
	td:nth-of-type(4) {
		width: 300px;
	}

	th:nth-of-type(5),
	td:nth-of-type(5) {
		font-size: 18px;
		width: 100px;
		text-align: center;

		a {
			margin: 0 5px;
		}

		.fa-save {
			color: $green;
		}

		.fa-times-circle {
			color: $red;
		}
	}
}